import torch
import transformers
import datasets
from datasets import load_dataset, Audio
from evaluate import load
import argparse

def get_whisper(model_id):
    import torch
    def skip(*args, **kwargs):
        pass
    torch.nn.init.kaiming_uniform_ = skip
    torch.nn.init.uniform_ = skip
    torch.nn.init.normal_ = skip
    from transformers import AutoModelForSpeechSeq2Seq
    torch_dtype = torch.float16
    model = AutoModelForSpeechSeq2Seq.from_pretrained(
        model_id, torch_dtype=torch_dtype, low_cpu_mem_usage=True, use_safetensors=True
    )
    return model

def parse():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model", 
        type=str, 
        help="whisper model to load"
    )
    parser.add_argument(
        "--dataset",
        type=str,
        help="Where to extract calibration data from.",
    )
    parser.add_argument(
        "--language", 
        default="english"
    )
    parser.add_argument(
        "--task", 
        default="transcribe"
    )
    args = parser.parse_args()
    return args


def main(args):

    print(args.model)
    model = get_whisper(args.model)
    device = torch.device("cuda:0")
    model.to(device)
    model = torch.compile(model)
    from transformers import AutoProcessor
    processor = AutoProcessor.from_pretrained(args.model)
    whisper_norm = processor.tokenizer.normalize
    wer = load("wer")

    dataset_dict = {}
    language_list = args.language.split(",")
    
    if "fr" in language_list:
        mls_french_test = torch.load("/exp/tianteng.gu/mls/french_test.pth")
        dataset_dict["fr"] = datasets.Dataset.from_list(mls_french_test)
        del mls_french_test
    if "de" in language_list:
        mls_german_test = torch.load("/exp/tianteng.gu/mls/german_test.pth")
        dataset_dict["de"] = datasets.Dataset.from_list(mls_german_test)
        del mls_german_test
        # dataset_dict["de"] = load_dataset("facebook/multilingual_librispeech", "german", split="test", cache_dir="/exp/tianteng.gu/huggingface/cache")
    if "es" in language_list:
        mls_spanish_test = torch.load("/exp/tianteng.gu/mls/spanish_test.pth")
        dataset_dict["es"] = datasets.Dataset.from_list(mls_spanish_test)
        del mls_spanish_test
        # dataset_dict["es"] = load_dataset("facebook/multilingual_librispeech", "spanish", split="test", cache_dir="/exp/tianteng.gu/huggingface/cache")
    if "it" in language_list:
        mls_italian_test = torch.load("/exp/tianteng.gu/mls/italian_test.pth")
        dataset_dict["it"] = datasets.Dataset.from_list(mls_italian_test)
        del mls_italian_test
        # dataset_dict["it"] = load_dataset("facebook/multilingual_librispeech", "italian", split="test", cache_dir="/exp/tianteng.gu/huggingface/cache")

    def get_text(sample):
        if "text" in sample:
            return sample["text"]
        elif "sentence" in sample:
            return sample["sentence"]
        elif "normalized_text" in sample:
            return sample["normalized_text"]
        elif "transcript" in sample:
            return sample["transcript"]
        elif "transcription" in sample:
            return sample["transcription"]
        else:
            raise ValueError(f"Sample: {sample.keys()} has no transcript.")

    def normalise(batch):
        batch["norm_text"] = whisper_norm(get_text(batch))
        return batch

    def map_language(language):
        def map_to_pred(batch):
            input_features = processor([batch["audio"][i]["array"] for i in range(len(batch["audio"]))], sampling_rate=16000, return_tensors="pt").input_features
            input_features = input_features.to(model.dtype)

            with torch.no_grad():
                predicted_ids = model.generate(input_features.to("cuda"), language=language, task="transcribe")
                transcription = processor.batch_decode(predicted_ids)
                batch["prediction"] = [processor.tokenizer.normalize(transcription[i]) for i in range(len(batch["audio"]))]
            return batch
        return map_to_pred

    filter_sequences = ["ignore time segment in scoring", ""]
    def is_target_text_in_range(ref):
        ref = ref.strip()
        return ref not in filter_sequences

    for name, dataset in dataset_dict.items():
        print(name)
        dataset = dataset.map(normalise)
        dataset = dataset.filter(is_target_text_in_range, input_columns=["norm_text"])
        dataset = dataset.cast_column("audio", Audio(sampling_rate=16000))
        map_to_pred = map_language(name)
        result = dataset.map(map_to_pred, batched=True, batch_size=16)
        print(100 * wer.compute(references=result["norm_text"], predictions=result["prediction"]))

    print(args.model)
    
if __name__ == '__main__':
    args = parse()
    main(args)
