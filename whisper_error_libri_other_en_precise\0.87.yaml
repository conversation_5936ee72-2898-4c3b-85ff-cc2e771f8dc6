decoder:
- encoder_attn.k_proj: 15.421875
  encoder_attn.out_proj: 378.75
  encoder_attn.q_proj: 6.109375
  encoder_attn.v_proj: 13.796875
  fc1: 15.609375
  fc2: 6.05859375
  self_attn.k_proj: 30.859375
  self_attn.out_proj: 2.830078125
  self_attn.q_proj: 25.046875
  self_attn.v_proj: 8.0
- encoder_attn.k_proj: 16.3125
  encoder_attn.out_proj: 149.375
  encoder_attn.q_proj: 1.0419921875
  encoder_attn.v_proj: 14.4609375
  fc1: 6.96484375
  fc2: 1.662109375
  self_attn.k_proj: 2.28515625
  self_attn.out_proj: 0.059783935546875
  self_attn.q_proj: 2.30078125
  self_attn.v_proj: 0.494384765625
- encoder_attn.k_proj: 9.671875
  encoder_attn.out_proj: 34.15625
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 8.6015625
  fc1: 12.390625
  fc2: 0.68115234375
  self_attn.k_proj: 0.62109375
  self_attn.out_proj: 0.054718017578125
  self_attn.q_proj: 1.1865234375
  self_attn.v_proj: 0.25
- encoder_attn.k_proj: 8.0
  encoder_attn.out_proj: 55.3125
  encoder_attn.q_proj: 0.062469482421875
  encoder_attn.v_proj: 6.01171875
  fc1: 6.43359375
  fc2: 1.2060546875
  self_attn.k_proj: 0.2333984375
  self_attn.out_proj: 0.04730224609375
  self_attn.q_proj: 0.479248046875
  self_attn.v_proj: 0.2396240234375
- encoder_attn.k_proj: 15.546875
  encoder_attn.out_proj: 135.375
  encoder_attn.q_proj: 0.74951171875
  encoder_attn.v_proj: 13.5234375
  fc1: 6.24609375
  fc2: 1.0029296875
  self_attn.k_proj: 0.422607421875
  self_attn.out_proj: 0.047332763671875
  self_attn.q_proj: 1.2119140625
  self_attn.v_proj: 0.32177734375
- encoder_attn.k_proj: 13.2578125
  encoder_attn.out_proj: 199.75
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 10.828125
  fc1: 12.484375
  fc2: 0.222900390625
  self_attn.k_proj: 0.74072265625
  self_attn.out_proj: 0.347412109375
  self_attn.q_proj: 1.0927734375
  self_attn.v_proj: 0.401611328125
- encoder_attn.k_proj: 21.9375
  encoder_attn.out_proj: 191.375
  encoder_attn.q_proj: 1.3916015625
  encoder_attn.v_proj: 17.9375
  fc1: 15.3046875
  fc2: 0.24365234375
  self_attn.k_proj: 1.1396484375
  self_attn.out_proj: 0.318115234375
  self_attn.q_proj: 1.3271484375
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 16.515625
  encoder_attn.out_proj: 253.625
  encoder_attn.q_proj: 1.9208984375
  encoder_attn.v_proj: 14.6875
  fc1: 15.796875
  fc2: 0.1708984375
  self_attn.k_proj: 1.5361328125
  self_attn.out_proj: 0.48828125
  self_attn.q_proj: 1.369140625
  self_attn.v_proj: 0.486328125
- encoder_attn.k_proj: 13.4921875
  encoder_attn.out_proj: 148.375
  encoder_attn.q_proj: 1.8212890625
  encoder_attn.v_proj: 9.7109375
  fc1: 31.46875
  fc2: 0.1270751953125
  self_attn.k_proj: 1.8681640625
  self_attn.out_proj: 0.406005859375
  self_attn.q_proj: 1.5400390625
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 14.109375
  encoder_attn.out_proj: 370.5
  encoder_attn.q_proj: 2.51171875
  encoder_attn.v_proj: 13.2421875
  fc1: 29.015625
  fc2: 0.2015380859375
  self_attn.k_proj: 1.7685546875
  self_attn.out_proj: 0.31787109375
  self_attn.q_proj: 1.8232421875
  self_attn.v_proj: 0.479248046875
- encoder_attn.k_proj: 21.65625
  encoder_attn.out_proj: 193.375
  encoder_attn.q_proj: 1.8046875
  encoder_attn.v_proj: 13.71875
  fc1: 29.421875
  fc2: 0.140625
  self_attn.k_proj: 2.71875
  self_attn.out_proj: 0.4716796875
  self_attn.q_proj: 2.265625
  self_attn.v_proj: 0.7412109375
- encoder_attn.k_proj: 21.578125
  encoder_attn.out_proj: 409.0
  encoder_attn.q_proj: 0.77685546875
  encoder_attn.v_proj: 16.40625
  fc1: 24.65625
  fc2: 0.15576171875
  self_attn.k_proj: 1.9150390625
  self_attn.out_proj: 0.335693359375
  self_attn.q_proj: 1.966796875
  self_attn.v_proj: 0.64990234375
- encoder_attn.k_proj: 22.796875
  encoder_attn.out_proj: 156.375
  encoder_attn.q_proj: 1.4306640625
  encoder_attn.v_proj: 19.484375
  fc1: 16.421875
  fc2: 0.25
  self_attn.k_proj: 2.498046875
  self_attn.out_proj: 0.7392578125
  self_attn.q_proj: 2.455078125
  self_attn.v_proj: 0.939453125
- encoder_attn.k_proj: 25.546875
  encoder_attn.out_proj: 479.25
  encoder_attn.q_proj: 1.396484375
  encoder_attn.v_proj: 19.265625
  fc1: 11.6875
  fc2: 0.5986328125
  self_attn.k_proj: 2.326171875
  self_attn.out_proj: 0.55419921875
  self_attn.q_proj: 1.96875
  self_attn.v_proj: 1.0
- encoder_attn.k_proj: 25.53125
  encoder_attn.out_proj: 324.75
  encoder_attn.q_proj: 0.77587890625
  encoder_attn.v_proj: 20.140625
  fc1: 13.703125
  fc2: 0.287353515625
  self_attn.k_proj: 2.02734375
  self_attn.out_proj: 0.452392578125
  self_attn.q_proj: 2.16796875
  self_attn.v_proj: 0.83056640625
- encoder_attn.k_proj: 26.765625
  encoder_attn.out_proj: 359.25
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 21.375
  fc1: 12.1640625
  fc2: 0.384765625
  self_attn.k_proj: 2.537109375
  self_attn.out_proj: 0.65869140625
  self_attn.q_proj: 2.64453125
  self_attn.v_proj: 1.1533203125
- encoder_attn.k_proj: 27.84375
  encoder_attn.out_proj: 771.0
  encoder_attn.q_proj: 0.966796875
  encoder_attn.v_proj: 22.9375
  fc1: 13.421875
  fc2: 0.312255859375
  self_attn.k_proj: 2.048828125
  self_attn.out_proj: 0.496337890625
  self_attn.q_proj: 2.0
  self_attn.v_proj: 0.9892578125
- encoder_attn.k_proj: 26.90625
  encoder_attn.out_proj: 517.0
  encoder_attn.q_proj: 1.310546875
  encoder_attn.v_proj: 21.90625
  fc1: 11.6328125
  fc2: 0.27490234375
  self_attn.k_proj: 1.8466796875
  self_attn.out_proj: 0.8876953125
  self_attn.q_proj: 1.9609375
  self_attn.v_proj: 1.1220703125
- encoder_attn.k_proj: 27.25
  encoder_attn.out_proj: 823.0
  encoder_attn.q_proj: 1.9765625
  encoder_attn.v_proj: 23.265625
  fc1: 11.8203125
  fc2: 0.25
  self_attn.k_proj: 2.228515625
  self_attn.out_proj: 0.47802734375
  self_attn.q_proj: 2.005859375
  self_attn.v_proj: 1.2314453125
- encoder_attn.k_proj: 28.796875
  encoder_attn.out_proj: 647.0
  encoder_attn.q_proj: 1.5439453125
  encoder_attn.v_proj: 25.390625
  fc1: 15.578125
  fc2: 0.3095703125
  self_attn.k_proj: 1.8095703125
  self_attn.out_proj: 0.316162109375
  self_attn.q_proj: 1.748046875
  self_attn.v_proj: 0.75732421875
- encoder_attn.k_proj: 29.625
  encoder_attn.out_proj: 870.0
  encoder_attn.q_proj: 2.142578125
  encoder_attn.v_proj: 24.765625
  fc1: 11.125
  fc2: 0.342529296875
  self_attn.k_proj: 2.783203125
  self_attn.out_proj: 0.626953125
  self_attn.q_proj: 2.05078125
  self_attn.v_proj: 1.009765625
- encoder_attn.k_proj: 29.390625
  encoder_attn.out_proj: 564.0
  encoder_attn.q_proj: 1.734375
  encoder_attn.v_proj: 24.5
  fc1: 13.140625
  fc2: 0.310791015625
  self_attn.k_proj: 1.7021484375
  self_attn.out_proj: 1.0166015625
  self_attn.q_proj: 1.7607421875
  self_attn.v_proj: 0.91845703125
- encoder_attn.k_proj: 28.171875
  encoder_attn.out_proj: 610.0
  encoder_attn.q_proj: 2.361328125
  encoder_attn.v_proj: 28.515625
  fc1: 8.71875
  fc2: 0.1610107421875
  self_attn.k_proj: 3.18359375
  self_attn.out_proj: 0.35986328125
  self_attn.q_proj: 2.412109375
  self_attn.v_proj: 1.1982421875
- encoder_attn.k_proj: 31.203125
  encoder_attn.out_proj: 729.5
  encoder_attn.q_proj: 2.0
  encoder_attn.v_proj: 25.921875
  fc1: 7.8515625
  fc2: 0.373779296875
  self_attn.k_proj: 1.7421875
  self_attn.out_proj: 0.4130859375
  self_attn.q_proj: 1.517578125
  self_attn.v_proj: 0.8203125
- encoder_attn.k_proj: 29.125
  encoder_attn.out_proj: 588.5
  encoder_attn.q_proj: 1.4921875
  encoder_attn.v_proj: 25.109375
  fc1: 6.05078125
  fc2: 0.35888671875
  self_attn.k_proj: 1.89453125
  self_attn.out_proj: 0.767578125
  self_attn.q_proj: 1.4560546875
  self_attn.v_proj: 0.7138671875
- encoder_attn.k_proj: 30.828125
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.36328125
  encoder_attn.v_proj: 26.546875
  fc1: 8.0
  fc2: 0.38037109375
  self_attn.k_proj: 1.818359375
  self_attn.out_proj: 0.48974609375
  self_attn.q_proj: 1.640625
  self_attn.v_proj: 0.97021484375
- encoder_attn.k_proj: 32.1875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.412109375
  encoder_attn.v_proj: 24.234375
  fc1: 7.6171875
  fc2: 0.3076171875
  self_attn.k_proj: 1.9638671875
  self_attn.out_proj: 0.351806640625
  self_attn.q_proj: 1.59375
  self_attn.v_proj: 0.85107421875
- encoder_attn.k_proj: 32.0
  encoder_attn.out_proj: 682.0
  encoder_attn.q_proj: 1.1494140625
  encoder_attn.v_proj: 28.390625
  fc1: 8.90625
  fc2: 0.330810546875
  self_attn.k_proj: 1.671875
  self_attn.out_proj: 0.459228515625
  self_attn.q_proj: 1.734375
  self_attn.v_proj: 0.97705078125
- encoder_attn.k_proj: 33.0
  encoder_attn.out_proj: 751.5
  encoder_attn.q_proj: 1.0625
  encoder_attn.v_proj: 26.421875
  fc1: 10.1953125
  fc2: 0.366455078125
  self_attn.k_proj: 1.7373046875
  self_attn.out_proj: 0.796875
  self_attn.q_proj: 1.7724609375
  self_attn.v_proj: 0.73828125
- encoder_attn.k_proj: 32.0
  encoder_attn.out_proj: 630.0
  encoder_attn.q_proj: 1.1376953125
  encoder_attn.v_proj: 24.96875
  fc1: 10.9375
  fc2: 0.364990234375
  self_attn.k_proj: 1.9736328125
  self_attn.out_proj: 0.376708984375
  self_attn.q_proj: 1.9228515625
  self_attn.v_proj: 0.82275390625
- encoder_attn.k_proj: 35.0625
  encoder_attn.out_proj: 868.5
  encoder_attn.q_proj: 1.330078125
  encoder_attn.v_proj: 27.359375
  fc1: 9.203125
  fc2: 0.45751953125
  self_attn.k_proj: 2.2578125
  self_attn.out_proj: 0.57421875
  self_attn.q_proj: 2.0
  self_attn.v_proj: 1.0966796875
- encoder_attn.k_proj: 36.6875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 0.94580078125
  encoder_attn.v_proj: 29.25
  fc1: 7.51953125
  fc2: 0.64990234375
  self_attn.k_proj: 1.921875
  self_attn.out_proj: 0.498046875
  self_attn.q_proj: 2.109375
  self_attn.v_proj: 0.587890625
encoder:
- fc1: .inf
  fc2: 0.2166748046875
  self_attn.k_proj: .inf
  self_attn.out_proj: 0.291259765625
  self_attn.q_proj: 4.6328125
  self_attn.v_proj: 3.16796875
- fc1: 3.275390625
  fc2: 0.0394287109375
  self_attn.k_proj: 0.9443359375
  self_attn.out_proj: 0.11761474609375
  self_attn.q_proj: 0.81103515625
  self_attn.v_proj: 0.425048828125
- fc1: 3.935546875
  fc2: 0.01220703125
  self_attn.k_proj: 0.6962890625
  self_attn.out_proj: 0.12213134765625
  self_attn.q_proj: 0.62548828125
  self_attn.v_proj: 0.405029296875
- fc1: 4.796875
  fc2: 0.018707275390625
  self_attn.k_proj: 0.8017578125
  self_attn.out_proj: 0.0946044921875
  self_attn.q_proj: 0.6337890625
  self_attn.v_proj: 0.4287109375
- fc1: 3.35546875
  fc2: 0.013427734375
  self_attn.k_proj: 0.798828125
  self_attn.out_proj: 0.09161376953125
  self_attn.q_proj: 0.60595703125
  self_attn.v_proj: 0.43701171875
- fc1: 4.12890625
  fc2: 0.0150604248046875
  self_attn.k_proj: 0.76025390625
  self_attn.out_proj: 0.11474609375
  self_attn.q_proj: 0.54150390625
  self_attn.v_proj: 0.464599609375
- fc1: .inf
  fc2: 0.0164947509765625
  self_attn.k_proj: 1.099609375
  self_attn.out_proj: 0.10516357421875
  self_attn.q_proj: 0.787109375
  self_attn.v_proj: 0.5634765625
- fc1: .inf
  fc2: 0.024810791015625
  self_attn.k_proj: 1.2099609375
  self_attn.out_proj: 0.11663818359375
  self_attn.q_proj: 0.89404296875
  self_attn.v_proj: 0.58935546875
- fc1: .inf
  fc2: 0.037445068359375
  self_attn.k_proj: 1.2509765625
  self_attn.out_proj: 0.1304931640625
  self_attn.q_proj: 0.93115234375
  self_attn.v_proj: 0.611328125
- fc1: .inf
  fc2: 0.029754638671875
  self_attn.k_proj: 1.19140625
  self_attn.out_proj: 0.10906982421875
  self_attn.q_proj: 0.85693359375
  self_attn.v_proj: 0.60693359375
- fc1: .inf
  fc2: 0.034759521484375
  self_attn.k_proj: 0.984375
  self_attn.out_proj: 0.138671875
  self_attn.q_proj: 0.76025390625
  self_attn.v_proj: 0.61279296875
- fc1: .inf
  fc2: 0.03350830078125
  self_attn.k_proj: 1.169921875
  self_attn.out_proj: 0.128662109375
  self_attn.q_proj: 0.8095703125
  self_attn.v_proj: 0.60791015625
- fc1: .inf
  fc2: 0.049957275390625
  self_attn.k_proj: 1.1796875
  self_attn.out_proj: 0.1278076171875
  self_attn.q_proj: 0.79736328125
  self_attn.v_proj: 0.55615234375
- fc1: 4.64453125
  fc2: 0.0687255859375
  self_attn.k_proj: 1.0263671875
  self_attn.out_proj: 0.1383056640625
  self_attn.q_proj: 0.734375
  self_attn.v_proj: 0.5224609375
- fc1: .inf
  fc2: 0.0869140625
  self_attn.k_proj: 1.1826171875
  self_attn.out_proj: 0.09918212890625
  self_attn.q_proj: 0.837890625
  self_attn.v_proj: 0.52197265625
- fc1: .inf
  fc2: 0.08111572265625
  self_attn.k_proj: 1.0859375
  self_attn.out_proj: 0.1260986328125
  self_attn.q_proj: 0.80078125
  self_attn.v_proj: 0.51806640625
- fc1: .inf
  fc2: 0.09765625
  self_attn.k_proj: 0.99658203125
  self_attn.out_proj: 0.1409912109375
  self_attn.q_proj: 0.669921875
  self_attn.v_proj: 0.53857421875
- fc1: .inf
  fc2: 0.11541748046875
  self_attn.k_proj: 1.302734375
  self_attn.out_proj: 0.13916015625
  self_attn.q_proj: 0.9873046875
  self_attn.v_proj: 0.658203125
- fc1: .inf
  fc2: 0.1475830078125
  self_attn.k_proj: 1.3349609375
  self_attn.out_proj: 0.1630859375
  self_attn.q_proj: 0.93701171875
  self_attn.v_proj: 0.6572265625
- fc1: .inf
  fc2: 0.1781005859375
  self_attn.k_proj: 1.26171875
  self_attn.out_proj: 0.15869140625
  self_attn.q_proj: 1.0322265625
  self_attn.v_proj: 0.66064453125
- fc1: .inf
  fc2: 0.2288818359375
  self_attn.k_proj: 1.4755859375
  self_attn.out_proj: 0.2320556640625
  self_attn.q_proj: 1.3876953125
  self_attn.v_proj: 0.78173828125
- fc1: .inf
  fc2: 0.2156982421875
  self_attn.k_proj: 1.498046875
  self_attn.out_proj: 0.19384765625
  self_attn.q_proj: 1.373046875
  self_attn.v_proj: 0.79443359375
- fc1: .inf
  fc2: 0.224609375
  self_attn.k_proj: 1.73828125
  self_attn.out_proj: 0.255126953125
  self_attn.q_proj: 1.7353515625
  self_attn.v_proj: 0.892578125
- fc1: .inf
  fc2: 0.23388671875
  self_attn.k_proj: 1.6845703125
  self_attn.out_proj: 0.219970703125
  self_attn.q_proj: 1.69921875
  self_attn.v_proj: 0.9267578125
- fc1: .inf
  fc2: 0.25244140625
  self_attn.k_proj: 2.005859375
  self_attn.out_proj: 0.257568359375
  self_attn.q_proj: 1.8916015625
  self_attn.v_proj: 1.03125
- fc1: .inf
  fc2: 0.279052734375
  self_attn.k_proj: 1.9091796875
  self_attn.out_proj: 0.240966796875
  self_attn.q_proj: 1.8427734375
  self_attn.v_proj: 0.99267578125
- fc1: .inf
  fc2: 0.28857421875
  self_attn.k_proj: 2.076171875
  self_attn.out_proj: 0.2325439453125
  self_attn.q_proj: 1.9921875
  self_attn.v_proj: 1.08984375
- fc1: .inf
  fc2: 0.32470703125
  self_attn.k_proj: 1.9853515625
  self_attn.out_proj: 0.1807861328125
  self_attn.q_proj: 1.7548828125
  self_attn.v_proj: 1.0224609375
- fc1: .inf
  fc2: 0.37841796875
  self_attn.k_proj: 2.12890625
  self_attn.out_proj: 0.294677734375
  self_attn.q_proj: 1.951171875
  self_attn.v_proj: 1.1962890625
- fc1: .inf
  fc2: 0.409912109375
  self_attn.k_proj: 2.419921875
  self_attn.out_proj: 0.386962890625
  self_attn.q_proj: 2.37109375
  self_attn.v_proj: 1.4453125
- fc1: .inf
  fc2: 0.474365234375
  self_attn.k_proj: 2.34765625
  self_attn.out_proj: 0.404541015625
  self_attn.q_proj: 2.33203125
  self_attn.v_proj: 1.5068359375
- fc1: .inf
  fc2: 0.53515625
  self_attn.k_proj: 2.470703125
  self_attn.out_proj: 0.45751953125
  self_attn.q_proj: 2.564453125
  self_attn.v_proj: 1.6123046875
