# export CUDA_VISIBLE_DEVICES=1

model=openai/whisper-large-v3

/home/<USER>/miniconda3/envs/sparse/bin/python /exp/tianteng.gu/projects/sparsegpt-master/whisper_ablation_audio.py  \
    --model $model \
    --dataset libri_other \
    --language en \
    --task transcribe \
    --nsamples 2048 \
    --blocksize 256 \
    --sparsity_dict_path /exp/tianteng.gu/projects/sparsegpt-master/mix_config/uniform_0.7.yaml \
    --eval false \
    --save /exp/tianteng.gu/projects/sparsegpt-master/ablation_exp/audio_0.7