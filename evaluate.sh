# export CUDA_VISIBLE_DEVICES=1

model=/exp/tianteng.gu/projects/CLAQ-main/exp/log-AdaPre
dataset=voxpopuli,fleurs,ls_clean,ls_other,tedlium,cv5
language=english
task=transcribe
                        
python /exp/tianteng.gu/projects/sparsegpt-master/whisper_evaluate.py \
    --model $model \
    --dataset $dataset \
    --language $language \
    --task $task

# model=/exp/tianteng.gu/projects/sparsegpt-master/ablation_exp/text_0.6
# dataset=cv5 #voxpopuli,fleurs,ls_clean,ls_other,tedlium,cv5
# language=english
# task=transcribe
                        
# python /exp/tianteng.gu/projects/sparsegpt-master/whisper_evaluate.py \
#     --model $model \
#     --dataset $dataset \
#     --language $language \
#     --task $task