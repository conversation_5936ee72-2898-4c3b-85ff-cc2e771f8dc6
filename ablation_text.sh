model=openai/whisper-large-v3

/home/<USER>/miniconda3/envs/sparse/bin/python /exp/tianteng.gu/projects/sparsegpt-master/whisper_ablation_text.py  \
    --model $model \
    --nsamples 2048 \
    --blocksize 256 \
    --sparsity_dict_path /exp/tianteng.gu/projects/sparsegpt-master/mix_config/uniform_0.7.yaml \
    --eval false \
    --save /exp/tianteng.gu/projects/sparsegpt-master/ablation_exp/text_0.7