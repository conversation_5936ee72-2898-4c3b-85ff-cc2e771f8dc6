decoder:
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.5
  encoder_attn.v_proj: 0.5
  fc1: 0.33
  fc2: 0.44
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.3
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.3
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.36
  fc2: 0.68
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.77
  self_attn.k_proj: 0.78
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.67
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.78
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.71
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.76
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.76
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.73
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.72
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.66
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.78
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.77
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.67
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.76
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.66
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.66
  self_attn.v_proj: 0.76
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.76
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.62
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.73
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.77
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.77
  self_attn.k_proj: 0.6
  self_attn.out_proj: 0.61
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.62
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.58
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.69
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.69
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.77
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.57
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.57
  self_attn.out_proj: 0.58
  self_attn.q_proj: 0.59
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.72
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.63
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.73
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.73
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.6
  self_attn.out_proj: 0.64
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.71
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.31
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.59
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.69
  encoder_attn.v_proj: 0.5
  fc1: 0.31
  fc2: 0.8
  self_attn.k_proj: 0.6
  self_attn.out_proj: 0.64
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.72
  encoder_attn.v_proj: 0.5
  fc1: 0.32
  fc2: 0.79
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.72
  encoder_attn.v_proj: 0.5
  fc1: 0.32
  fc2: 0.8
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.62
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.72
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.73
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.64
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.71
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.64
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.76
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.76
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.71
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.3
  fc2: 0.8
  self_attn.k_proj: 0.55
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.58
  self_attn.v_proj: 0.68
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.34
  fc2: 0.78
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.78
encoder:
- fc1: 0.3
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.3
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.3
- fc1: 0.49
  fc2: 0.8
  self_attn.k_proj: 0.77
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.45
  fc2: 0.8
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.44
  fc2: 0.8
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.45
  fc2: 0.8
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.42
  fc2: 0.8
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.4
  fc2: 0.8
  self_attn.k_proj: 0.74
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.8
- fc1: 0.41
  fc2: 0.8
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.76
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.76
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.77
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.78
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.74
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.77
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.73
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.78
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.8
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.79
  self_attn.q_proj: 0.73
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.77
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.77
  self_attn.q_proj: 0.72
  self_attn.v_proj: 0.8
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.76
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.63
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.63
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.71
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.58
  self_attn.v_proj: 0.71
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.61
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.67
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.67
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.67
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.51
  self_attn.out_proj: 0.64
  self_attn.q_proj: 0.52
  self_attn.v_proj: 0.64
- fc1: 0.5
  fc2: 0.8
  self_attn.k_proj: 0.52
  self_attn.out_proj: 0.73
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.66
- fc1: 0.5
  fc2: 0.77
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.59
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.61
- fc1: 0.5
  fc2: 0.74
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.51
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.56
- fc1: 0.5
  fc2: 0.7
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.51
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.55
- fc1: 0.5
  fc2: 0.68
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.45
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.54
