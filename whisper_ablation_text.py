import time

import torch
import torch.nn as nn

from modelutils import *
from datautils_whisper import *
from sparsegpt import *
import yaml
import os

from tqdm import tqdm

def get_whisper(model):
    import torch
    def skip(*args, **kwargs):
        pass
    torch.nn.init.kaiming_uniform_ = skip
    torch.nn.init.uniform_ = skip
    torch.nn.init.normal_ = skip
    from transformers import AutoModelForSpeechSeq2Seq
    torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    # torch_dtype = torch.float32
    model_id = "openai/whisper-large-v3"
    model = AutoModelForSpeechSeq2Seq.from_pretrained(
        model_id, torch_dtype=torch_dtype
    )
    return model

@torch.no_grad()
def whisper_sequential(model, dataloader, text_dataloader, dev):

    for i in range(len(dataloader)):
        dataloader[i] = dataloader[i].to(model.dtype)

    with open(args.sparsity_dict_path, 'r') as file:
        sparsity_dict = yaml.safe_load(file)

    # print('Generating encoder hidden states ...')
    # encoder_hidden_states = []
    # model.model.encoder = model.model.encoder.to(dev)
    # batch_size = 16
    # for i in tqdm(range(0, len(dataloader), 16)):
    #     start = i
    #     end = min(len(dataloader), start+batch_size)
    #     batch = dataloader[start:end]
    #     batch = torch.concatenate(batch, dim=0)
    #     e_h_s = model.model.encoder(batch.to(dev))[0].cpu()
    #     encoder_hidden_states += [x.unsqueeze(0) for x in e_h_s]
    # model.model.encoder = model.model.encoder.cpu()

    predicted_ids = text_dataloader

    use_cache = model.config.use_cache
    model.config.use_cache = False

    ######### Encoder ##################
    print('Pruning encoder')
    layers = model.model.encoder.layers

    model.model.encoder.conv1 = model.model.encoder.conv1.to(dev)
    model.model.encoder.conv2 = model.model.encoder.conv2.to(dev)
    model.model.encoder.embed_positions = model.model.encoder.embed_positions.to(dev)
    layers[0] = layers[0].to(dev)

    dtype = next(iter(model.parameters())).dtype
    inps = [0 for i in range(len(dataloader))]
    cache = {'i': 0, 'attention_mask': None}

    class Catcher_encoder(torch.nn.Module):
        def __init__(self, module):
            super().__init__()
            self.module = module
        def forward(self, inp, attention_mask, layer_head_mask, **kwargs):
            inps[cache['i']] = inp
            cache['i'] += 1
            cache['attention_mask'] = attention_mask
            raise ValueError
    layers[0] = Catcher_encoder(layers[0])
    for batch in dataloader:
        try:
            model(batch.to(dev))
        except ValueError:
            pass
    layers[0] = layers[0].module

    layers[0] = layers[0].cpu()
    model.model.encoder.conv1 = model.model.encoder.conv1.cpu()
    model.model.encoder.conv2 = model.model.encoder.conv2.cpu()
    model.model.encoder.embed_positions = model.model.encoder.embed_positions.cpu()
    torch.cuda.empty_cache()

    outs = [0 for i in range(len(dataloader))]
    attention_mask = cache['attention_mask']

    print('Ready.')

    quantizers = {}
    for i in range(len(layers)):
        layer = layers[i].to(dev)
        full = find_layers(layer)

        if args.true_sequential:
            sequential = [
                ['self_attn.k_proj', 'self_attn.v_proj', 'self_attn.q_proj'],
                ['self_attn.out_proj'],
                ['fc1'],
                ['fc2']
            ]
        else:
            sequential = [list(full.keys())]
       
        for names in sequential:
            subset = {n: full[n] for n in names}

            gpts = {}
            for name in subset:
                gpts[name] = SparseGPT(subset[name])

            def add_batch(name):
                def tmp(_, inp, out):
                    gpts[name].add_batch(inp[0].data, out.data)
                return tmp
            handles = []
            for name in subset:
                handles.append(subset[name].register_forward_hook(add_batch(name)))
            for j in range(args.nsamples):
                outs[j] = layer(inps[j], attention_mask=attention_mask, layer_head_mask=None)[0]
            for h in handles:
                h.remove()

            for name in subset:
                print(i, name)
                layername = '.'+str(i)+'.'+name
                sparsity = sparsity_dict["encoder"][i][name]
                gpts[name].fasterprune(
                    sparsity,
                    prunen=args.prunen,
                    prunem=args.prunem,
                    percdamp=args.percdamp,
                    blocksize=args.blocksize,
                )
                gpts[name].free()

        for j in range(args.nsamples):
            outs[j] = layer(inps[j], attention_mask=attention_mask, layer_head_mask=None)[0]

        layers[i] = layer.cpu()
        del layer
        del gpts 
        torch.cuda.empty_cache()

        inps, outs = outs, inps
    
    del inps
    del outs
    torch.cuda.empty_cache()

    print('Generating encoder hidden states ...')
    encoder_hidden_states = []
    model.model.encoder = model.model.encoder.to(dev)
    batch_size = 16
    for i in tqdm(range(0, len(dataloader), 16)):
        start = i
        end = min(len(dataloader), start+batch_size)
        batch = dataloader[start:end]
        batch = torch.concatenate(batch, dim=0)
        e_h_s = model.model.encoder(batch.to(dev))[0].cpu()
        encoder_hidden_states += [x.unsqueeze(0) for x in e_h_s]
    model.model.encoder = model.model.encoder.cpu()

    ######### Decoder ##################
    print('Pruning decoder')
    attention_masks = [0 for i in range(len(dataloader))]
    dtype = next(iter(model.parameters())).dtype
    cache = {'i': 0}

    layers = model.model.decoder.layers

    model.model.decoder.embed_tokens = model.model.decoder.embed_tokens.to(dev)
    model.model.decoder.embed_positions = model.model.decoder.embed_positions.to(dev)
    layers[0] = layers[0].to(dev)

    decoder_inputs = [0 for i in range(len(dataloader))]
    decoder_outputs = [0 for i in range(len(dataloader))]

    class Catcher_decoder(torch.nn.Module):
        def __init__(self, module):
            super().__init__()
            self.module = module
        def forward(self, hiddenstates, **kwargs):
            decoder_inputs[cache['i']] = hiddenstates
            attention_masks[cache['i']] = kwargs['attention_mask']
            cache['i'] += 1
            raise ValueError

    layers[0] = Catcher_decoder(layers[0])
    for batch in predicted_ids:
        try:
            model.model.decoder(torch.tensor(batch).to(dev))
        except ValueError:
            pass
    layers[0] = layers[0].module

    layers[0] = layers[0].cpu()
    model.model.decoder.embed_tokens = model.model.decoder.embed_tokens.cpu()
    model.model.decoder.embed_positions = model.model.decoder.embed_positions.cpu()
    torch.cuda.empty_cache()

    print('Ready.')
    for i in range(len(layers)):
        layer = layers[i].to(dev)
        full = find_layers(layer)

        if args.true_sequential:
            sequential = [
                ['self_attn.k_proj', 'self_attn.v_proj', 'self_attn.q_proj'],
                ['self_attn.out_proj'],
                ['encoder_attn.k_proj', 'encoder_attn.v_proj', 'encoder_attn.q_proj'],
                ['encoder_attn.out_proj'],
                ['fc1'],
                ['fc2']
            ]
        else:
            sequential = [list(full.keys())]
       
        for names in sequential:
            subset = {n: full[n] for n in names}

            gpts = {}
            for name in subset:
                gpts[name] = SparseGPT(subset[name])

            def add_batch(name):
                def tmp(_, inp, out):
                    gpts[name].add_batch(inp[0].data, out.data)
                return tmp
            handles = []
            for name in subset:
                handles.append(subset[name].register_forward_hook(add_batch(name)))
            for j in range(args.nsamples):
                decoder_outputs[j] = layer(decoder_inputs[j], attention_mask=attention_masks[j], encoder_hidden_states=encoder_hidden_states[j].to(dev))[0]
            for h in handles:
                h.remove()

            for name in subset:
                print(i, name)
                layername = '.'+str(i)+'.'+name
                sparsity = sparsity_dict["decoder"][i][name]
                gpts[name].fasterprune(
                    sparsity,
                    prunen=args.prunen,
                    prunem=args.prunem,
                    percdamp=args.percdamp,
                    blocksize=args.blocksize,
                )
                gpts[name].free()

        for j in range(args.nsamples):
            decoder_outputs[j] = layer(decoder_inputs[j], attention_mask=attention_masks[j], encoder_hidden_states=encoder_hidden_states[j].to(dev))[0]

        layers[i] = layer.cpu()
        del layer
        del gpts 
        torch.cuda.empty_cache()

        decoder_inputs, decoder_outputs = decoder_outputs, decoder_inputs

    model.config.use_cache = use_cache
    
    return


@torch.no_grad()
def whisper_eval(model, device):

    model = model.to(device)

    from datasets import load_dataset
    from transformers import WhisperForConditionalGeneration, WhisperProcessor
    import torch
    from evaluate import load

    selected_ids = [i for i in range(0, 2620, 1)]
    librispeech_test_clean = load_dataset("/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr", "clean", split="test", cache_dir="/exp/tianteng.gu/huggingface/cache").select(selected_ids)

    processor = WhisperProcessor.from_pretrained("openai/whisper-large-v3")

    def map_to_pred(batch):
        audio = batch["audio"]
        input_features = processor(audio["array"], sampling_rate=audio["sampling_rate"], return_tensors="pt").input_features
        batch["reference"] = processor.tokenizer._normalize(batch['text'])

        with torch.no_grad():
            predicted_ids = model.generate(input_features.to(device))[0]
        transcription = processor.decode(predicted_ids)
        batch["prediction"] = processor.tokenizer._normalize(transcription)
        return batch

    result = librispeech_test_clean.map(map_to_pred)
    wer = load("wer")
    print(100 * wer.compute(references=result["reference"], predictions=result["prediction"]))

if __name__ == '__main__':
    import argparse
    from datautils_whisper import *

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--model", 
        type=str, 
        help="whisper model to load"
    )
    parser.add_argument(
        "--dataset",
        type=str,
        help="Where to extract calibration data from.",
    )
    parser.add_argument(
        "--seed", type=int, default=0, help="Seed for sampling the calibration data."
    )
    parser.add_argument(
        "--nsamples", type=int, default=128, help="Number of calibration data samples."
    )
    parser.add_argument(
        "--percdamp",
        type=float,
        default=0.01,
        help="Percent of the average Hessian diagonal to use for dampening.",
    )
    parser.add_argument("--sparsity_dict_path", type=str, default="", help="")
    parser.add_argument("--prunen", type=int, default=0, help="N for N:M pruning.")
    parser.add_argument("--prunem", type=int, default=0, help="M for N:M pruning.")
    parser.add_argument(
        "--blocksize",
        type=int,
        default=128,
        help="Blocksize to use for adaptive mask selection.",
    )
    parser.add_argument(
        "--gmp", action="store_true", help="Whether to run the GMP baseline."
    )
    parser.add_argument(
        "--wbits", type=int, default=16, help="Whether to quantize as well."
    )
    parser.add_argument(
        "--minlayer", type=int, default=-1, help="Prune all layers with id >= this."
    )
    parser.add_argument(
        "--maxlayer", type=int, default=1000, help="Prune all layers with id < this."
    )
    parser.add_argument(
        "--prune_only",
        type=str,
        default="",
        help="Prune only layers that contain this text.",
    )
    parser.add_argument("--invert", action="store_true", help="Invert subset.")
    parser.add_argument(
        "--true-sequential",
        action="store_true",
        help="Whether to run in true sequential model.",
    )
    parser.add_argument(
        "--log_wandb", action="store_true", help="Whether to log to wandb."
    )
    parser.add_argument(
        '--eval', type=str,default="false",
        help='whether to evaluate.'
    )
    parser.add_argument(
        "--save", 
        default="",
        help="Path to saved model."
    )
    parser.add_argument(
        "--language", 
        default=""
    )
    parser.add_argument(
        "--task", 
        default=""
    )
    args = parser.parse_args()

    model = get_whisper(args.model)
    model.eval()

    dataloader, text_dataloader = get_loaders(
        "libri_other_text", nsamples=args.nsamples, seed=args.seed, model_id=args.model, language=args.language
    )

    DEV = torch.device("cuda:0")

    tick = time.time()
    whisper_sequential(model, dataloader, text_dataloader, DEV)
    # for n, p in model.named_parameters():
    #     print(n, torch.mean((p == 0).float()))
    #     if 'down_proj' in n:
    #         break
    print(time.time() - tick)

    if args.eval == "true":
        whisper_eval(model, DEV)

    if args.save != "":
        if not os.path.exists(args.save):
            os.makedirs(args.save)
        model.save_pretrained(args.save)
        from transformers import AutoProcessor
        processor = AutoProcessor.from_pretrained(args.model)
        processor.save_pretrained(args.save)

    
