decoder:
- encoder_attn.k_proj: 0.51
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.55
  fc1: 0.77
  fc2: 0.52
  self_attn.k_proj: 0.55
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.84
- encoder_attn.k_proj: 0.53
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.56
  fc1: 0.78
  fc2: 0.68
  self_attn.k_proj: 0.85
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.75
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.67
  fc1: 0.73
  fc2: 0.71
  self_attn.k_proj: 0.85
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.85
  encoder_attn.out_proj: 0.63
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.78
  fc1: 0.76
  fc2: 0.72
  self_attn.k_proj: 0.85
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.67
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.61
  fc1: 0.78
  fc2: 0.81
  self_attn.k_proj: 0.85
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.69
  encoder_attn.out_proj: 0.51
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.61
  fc1: 0.71
  fc2: 0.85
  self_attn.k_proj: 0.85
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.57
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.82
  encoder_attn.v_proj: 0.56
  fc1: 0.69
  fc2: 0.82
  self_attn.k_proj: 0.84
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.83
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.51
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.51
  fc1: 0.66
  fc2: 0.85
  self_attn.k_proj: 0.81
  self_attn.out_proj: 0.84
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.66
  encoder_attn.out_proj: 0.51
  encoder_attn.q_proj: 0.84
  encoder_attn.v_proj: 0.62
  fc1: 0.63
  fc2: 0.85
  self_attn.k_proj: 0.77
  self_attn.out_proj: 0.83
  self_attn.q_proj: 0.77
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.53
  encoder_attn.out_proj: 0.64
  encoder_attn.q_proj: 0.82
  encoder_attn.v_proj: 0.53
  fc1: 0.61
  fc2: 0.85
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.51
  fc1: 0.62
  fc2: 0.77
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.71
  self_attn.v_proj: 0.84
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.5
  fc1: 0.64
  fc2: 0.82
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.77
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.85
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.77
  encoder_attn.v_proj: 0.5
  fc1: 0.66
  fc2: 0.73
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.8
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.52
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.67
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.67
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.77
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.67
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.81
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.5
  encoder_attn.q_proj: 0.84
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.66
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.85
  encoder_attn.q_proj: 0.81
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.66
  self_attn.k_proj: 0.66
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.66
  self_attn.v_proj: 0.77
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.56
  encoder_attn.q_proj: 0.82
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.65
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.79
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.85
  encoder_attn.q_proj: 0.8
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.65
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.57
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.64
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.81
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.71
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.69
  fc2: 0.64
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.81
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.68
  encoder_attn.q_proj: 0.71
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.64
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.79
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.63
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.79
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.79
  encoder_attn.q_proj: 0.69
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.64
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.78
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.77
  encoder_attn.q_proj: 0.69
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.64
  self_attn.k_proj: 0.66
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.66
  self_attn.v_proj: 0.78
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.85
  encoder_attn.q_proj: 0.68
  encoder_attn.v_proj: 0.5
  fc1: 0.69
  fc2: 0.63
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.73
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.85
  encoder_attn.q_proj: 0.71
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.63
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.73
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.79
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.57
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.61
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.69
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.83
  encoder_attn.q_proj: 0.69
  encoder_attn.v_proj: 0.5
  fc1: 0.65
  fc2: 0.62
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.78
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.62
  encoder_attn.q_proj: 0.72
  encoder_attn.v_proj: 0.5
  fc1: 0.64
  fc2: 0.62
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.72
  self_attn.q_proj: 0.69
  self_attn.v_proj: 0.79
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.83
  encoder_attn.q_proj: 0.74
  encoder_attn.v_proj: 0.5
  fc1: 0.64
  fc2: 0.62
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.63
  self_attn.v_proj: 0.72
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.85
  encoder_attn.q_proj: 0.85
  encoder_attn.v_proj: 0.5
  fc1: 0.71
  fc2: 0.63
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.81
  self_attn.q_proj: 0.77
  self_attn.v_proj: 0.85
encoder:
- fc1: 0.5
  fc2: 0.5
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.5
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.5
- fc1: 0.8
  fc2: 0.85
  self_attn.k_proj: 0.77
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.81
  self_attn.v_proj: 0.85
- fc1: 0.79
  fc2: 0.85
  self_attn.k_proj: 0.78
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.78
  self_attn.v_proj: 0.85
- fc1: 0.78
  fc2: 0.85
  self_attn.k_proj: 0.8
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.84
  self_attn.v_proj: 0.85
- fc1: 0.79
  fc2: 0.85
  self_attn.k_proj: 0.78
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.82
  self_attn.v_proj: 0.85
- fc1: 0.79
  fc2: 0.85
  self_attn.k_proj: 0.81
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.85
  self_attn.v_proj: 0.85
- fc1: 0.77
  fc2: 0.85
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.85
- fc1: 0.76
  fc2: 0.85
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.85
- fc1: 0.77
  fc2: 0.85
  self_attn.k_proj: 0.73
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.76
  self_attn.v_proj: 0.85
- fc1: 0.74
  fc2: 0.85
  self_attn.k_proj: 0.74
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.85
- fc1: 0.72
  fc2: 0.85
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.78
- fc1: 0.73
  fc2: 0.85
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.77
  self_attn.v_proj: 0.84
- fc1: 0.74
  fc2: 0.85
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.79
  self_attn.v_proj: 0.85
- fc1: 0.73
  fc2: 0.85
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.8
- fc1: 0.71
  fc2: 0.85
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.8
  self_attn.v_proj: 0.85
- fc1: 0.69
  fc2: 0.85
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.75
- fc1: 0.69
  fc2: 0.85
  self_attn.k_proj: 0.73
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.76
  self_attn.v_proj: 0.83
- fc1: 0.68
  fc2: 0.81
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.72
  self_attn.v_proj: 0.81
- fc1: 0.67
  fc2: 0.79
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.79
- fc1: 0.67
  fc2: 0.76
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.73
  self_attn.v_proj: 0.83
- fc1: 0.65
  fc2: 0.5
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.72
- fc1: 0.66
  fc2: 0.7
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.76
- fc1: 0.64
  fc2: 0.71
  self_attn.k_proj: 0.6
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.71
- fc1: 0.63
  fc2: 0.7
  self_attn.k_proj: 0.6
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.7
- fc1: 0.63
  fc2: 0.69
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.7
- fc1: 0.61
  fc2: 0.69
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.59
  self_attn.v_proj: 0.7
- fc1: 0.59
  fc2: 0.69
  self_attn.k_proj: 0.57
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.58
  self_attn.v_proj: 0.68
- fc1: 0.57
  fc2: 0.66
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.71
- fc1: 0.57
  fc2: 0.64
  self_attn.k_proj: 0.55
  self_attn.out_proj: 0.85
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.65
- fc1: 0.57
  fc2: 0.61
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.79
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.62
- fc1: 0.57
  fc2: 0.61
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.76
  self_attn.q_proj: 0.52
  self_attn.v_proj: 0.6
- fc1: 0.58
  fc2: 0.57
  self_attn.k_proj: 0.52
  self_attn.out_proj: 0.71
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.6
