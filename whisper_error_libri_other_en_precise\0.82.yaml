decoder:
- encoder_attn.k_proj: 12.1171875
  encoder_attn.out_proj: 316.75
  encoder_attn.q_proj: 4.2265625
  encoder_attn.v_proj: 12.9453125
  fc1: 3.556640625
  fc2: 3.375
  self_attn.k_proj: 22.6875
  self_attn.out_proj: 2.0859375
  self_attn.q_proj: 19.421875
  self_attn.v_proj: 7.03515625
- encoder_attn.k_proj: 12.6796875
  encoder_attn.out_proj: 122.1875
  encoder_attn.q_proj: 0.48681640625
  encoder_attn.v_proj: 13.34375
  fc1: 4.0
  fc2: 0.875
  self_attn.k_proj: 1.3876953125
  self_attn.out_proj: 0.03399658203125
  self_attn.q_proj: 1.158203125
  self_attn.v_proj: 0.248291015625
- encoder_attn.k_proj: 7.92578125
  encoder_attn.out_proj: 24.25
  encoder_attn.q_proj: 0.884765625
  encoder_attn.v_proj: 7.875
  fc1: 7.80859375
  fc2: 0.3857421875
  self_attn.k_proj: 0.36083984375
  self_attn.out_proj: 0.03125
  self_attn.q_proj: 0.60205078125
  self_attn.v_proj: 0.1322021484375
- encoder_attn.k_proj: 6.0859375
  encoder_attn.out_proj: 35.71875
  encoder_attn.q_proj: 0.033355712890625
  encoder_attn.v_proj: 5.25390625
  fc1: 4.0
  fc2: 0.72900390625
  self_attn.k_proj: 0.1644287109375
  self_attn.out_proj: 0.0213775634765625
  self_attn.q_proj: 0.27783203125
  self_attn.v_proj: 0.135009765625
- encoder_attn.k_proj: 12.2109375
  encoder_attn.out_proj: 106.875
  encoder_attn.q_proj: 0.58203125
  encoder_attn.v_proj: 11.8984375
  fc1: 3.6015625
  fc2: 0.623046875
  self_attn.k_proj: 0.2442626953125
  self_attn.out_proj: 0.0261993408203125
  self_attn.q_proj: 0.5908203125
  self_attn.v_proj: 0.1864013671875
- encoder_attn.k_proj: 10.0625
  encoder_attn.out_proj: 160.875
  encoder_attn.q_proj: 0.5
  encoder_attn.v_proj: 9.984375
  fc1: 7.70703125
  fc2: 0.1246337890625
  self_attn.k_proj: 0.496826171875
  self_attn.out_proj: 0.1868896484375
  self_attn.q_proj: 0.71728515625
  self_attn.v_proj: 0.276123046875
- encoder_attn.k_proj: 17.25
  encoder_attn.out_proj: 144.75
  encoder_attn.q_proj: 0.9716796875
  encoder_attn.v_proj: 15.8203125
  fc1: 8.0
  fc2: 0.1444091796875
  self_attn.k_proj: 0.7138671875
  self_attn.out_proj: 0.208251953125
  self_attn.q_proj: 0.78857421875
  self_attn.v_proj: 0.33984375
- encoder_attn.k_proj: 12.90625
  encoder_attn.out_proj: 197.625
  encoder_attn.q_proj: 1.267578125
  encoder_attn.v_proj: 13.7109375
  fc1: 7.9375
  fc2: 0.10699462890625
  self_attn.k_proj: 0.908203125
  self_attn.out_proj: 0.30322265625
  self_attn.q_proj: 0.8935546875
  self_attn.v_proj: 0.344970703125
- encoder_attn.k_proj: 10.8125
  encoder_attn.out_proj: 109.125
  encoder_attn.q_proj: 1.6884765625
  encoder_attn.v_proj: 8.875
  fc1: 15.53125
  fc2: 0.07611083984375
  self_attn.k_proj: 1.0
  self_attn.out_proj: 0.2462158203125
  self_attn.q_proj: 0.96240234375
  self_attn.v_proj: 0.376953125
- encoder_attn.k_proj: 11.1484375
  encoder_attn.out_proj: 295.75
  encoder_attn.q_proj: 1.794921875
  encoder_attn.v_proj: 12.4453125
  fc1: 13.6171875
  fc2: 0.10882568359375
  self_attn.k_proj: 0.99072265625
  self_attn.out_proj: 0.2039794921875
  self_attn.q_proj: 0.98876953125
  self_attn.v_proj: 0.344970703125
- encoder_attn.k_proj: 16.015625
  encoder_attn.out_proj: 156.625
  encoder_attn.q_proj: 1.0234375
  encoder_attn.v_proj: 12.6171875
  fc1: 14.1171875
  fc2: 0.092529296875
  self_attn.k_proj: 1.796875
  self_attn.out_proj: 0.282470703125
  self_attn.q_proj: 1.2861328125
  self_attn.v_proj: 0.47900390625
- encoder_attn.k_proj: 16.0
  encoder_attn.out_proj: 338.75
  encoder_attn.q_proj: 0.449462890625
  encoder_attn.v_proj: 15.59375
  fc1: 11.75
  fc2: 0.09869384765625
  self_attn.k_proj: 1.1337890625
  self_attn.out_proj: 0.2015380859375
  self_attn.q_proj: 1.123046875
  self_attn.v_proj: 0.414306640625
- encoder_attn.k_proj: 17.546875
  encoder_attn.out_proj: 123.1875
  encoder_attn.q_proj: 0.7685546875
  encoder_attn.v_proj: 18.015625
  fc1: 8.46875
  fc2: 0.1510009765625
  self_attn.k_proj: 1.6923828125
  self_attn.out_proj: 0.463134765625
  self_attn.q_proj: 1.4033203125
  self_attn.v_proj: 0.52392578125
- encoder_attn.k_proj: 19.734375
  encoder_attn.out_proj: 388.75
  encoder_attn.q_proj: 0.87060546875
  encoder_attn.v_proj: 17.75
  fc1: 6.7890625
  fc2: 0.356689453125
  self_attn.k_proj: 1.44140625
  self_attn.out_proj: 0.3583984375
  self_attn.q_proj: 1.255859375
  self_attn.v_proj: 0.6494140625
- encoder_attn.k_proj: 18.703125
  encoder_attn.out_proj: 261.0
  encoder_attn.q_proj: 0.451416015625
  encoder_attn.v_proj: 19.0
  fc1: 7.3515625
  fc2: 0.1875
  self_attn.k_proj: 1.2998046875
  self_attn.out_proj: 0.330078125
  self_attn.q_proj: 1.3837890625
  self_attn.v_proj: 0.505859375
- encoder_attn.k_proj: 20.53125
  encoder_attn.out_proj: 288.75
  encoder_attn.q_proj: 0.5830078125
  encoder_attn.v_proj: 19.78125
  fc1: 7.0234375
  fc2: 0.25
  self_attn.k_proj: 1.5625
  self_attn.out_proj: 0.447998046875
  self_attn.q_proj: 1.5146484375
  self_attn.v_proj: 0.73046875
- encoder_attn.k_proj: 21.078125
  encoder_attn.out_proj: 661.5
  encoder_attn.q_proj: 0.56591796875
  encoder_attn.v_proj: 21.21875
  fc1: 7.22265625
  fc2: 0.20263671875
  self_attn.k_proj: 1.4091796875
  self_attn.out_proj: 0.308349609375
  self_attn.q_proj: 1.3291015625
  self_attn.v_proj: 0.64599609375
- encoder_attn.k_proj: 20.703125
  encoder_attn.out_proj: 424.5
  encoder_attn.q_proj: 0.84375
  encoder_attn.v_proj: 20.65625
  fc1: 6.30078125
  fc2: 0.21875
  self_attn.k_proj: 1.1513671875
  self_attn.out_proj: 0.5
  self_attn.q_proj: 1.150390625
  self_attn.v_proj: 0.6875
- encoder_attn.k_proj: 20.765625
  encoder_attn.out_proj: 689.0
  encoder_attn.q_proj: 1.1357421875
  encoder_attn.v_proj: 21.546875
  fc1: 6.51953125
  fc2: 0.189453125
  self_attn.k_proj: 1.41796875
  self_attn.out_proj: 0.332275390625
  self_attn.q_proj: 1.353515625
  self_attn.v_proj: 0.74951171875
- encoder_attn.k_proj: 22.109375
  encoder_attn.out_proj: 531.5
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 23.453125
  fc1: 8.0
  fc2: 0.2208251953125
  self_attn.k_proj: 1.0234375
  self_attn.out_proj: 0.1920166015625
  self_attn.q_proj: 0.99365234375
  self_attn.v_proj: 0.50048828125
- encoder_attn.k_proj: 23.046875
  encoder_attn.out_proj: 725.0
  encoder_attn.q_proj: 1.19140625
  encoder_attn.v_proj: 23.0
  fc1: 6.1875
  fc2: 0.2225341796875
  self_attn.k_proj: 1.7841796875
  self_attn.out_proj: 0.35400390625
  self_attn.q_proj: 1.2783203125
  self_attn.v_proj: 0.630859375
- encoder_attn.k_proj: 22.625
  encoder_attn.out_proj: 498.0
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 22.359375
  fc1: 6.8828125
  fc2: 0.2008056640625
  self_attn.k_proj: 1.076171875
  self_attn.out_proj: 0.59716796875
  self_attn.q_proj: 1.07421875
  self_attn.v_proj: 0.544921875
- encoder_attn.k_proj: 22.078125
  encoder_attn.out_proj: 507.5
  encoder_attn.q_proj: 1.45703125
  encoder_attn.v_proj: 26.34375
  fc1: 4.8984375
  fc2: 0.12408447265625
  self_attn.k_proj: 1.8271484375
  self_attn.out_proj: 0.2476806640625
  self_attn.q_proj: 1.2646484375
  self_attn.v_proj: 0.6962890625
- encoder_attn.k_proj: 23.875
  encoder_attn.out_proj: 630.0
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 23.71875
  fc1: 4.28125
  fc2: 0.25
  self_attn.k_proj: 1.15625
  self_attn.out_proj: 0.267822265625
  self_attn.q_proj: 1.02734375
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 22.421875
  encoder_attn.out_proj: 504.25
  encoder_attn.q_proj: 0.9833984375
  encoder_attn.v_proj: 23.15625
  fc1: 3.935546875
  fc2: 0.23876953125
  self_attn.k_proj: 1.037109375
  self_attn.out_proj: 0.45751953125
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.47216796875
- encoder_attn.k_proj: 23.9375
  encoder_attn.out_proj: 725.0
  encoder_attn.q_proj: 0.93310546875
  encoder_attn.v_proj: 24.65625
  fc1: 4.79296875
  fc2: 0.256591796875
  self_attn.k_proj: 1.2236328125
  self_attn.out_proj: 0.29052734375
  self_attn.q_proj: 1.021484375
  self_attn.v_proj: 0.58154296875
- encoder_attn.k_proj: 25.578125
  encoder_attn.out_proj: 785.5
  encoder_attn.q_proj: 0.96142578125
  encoder_attn.v_proj: 22.34375
  fc1: 4.703125
  fc2: 0.1876220703125
  self_attn.k_proj: 1.25390625
  self_attn.out_proj: 0.24560546875
  self_attn.q_proj: 1.0927734375
  self_attn.v_proj: 0.53125
- encoder_attn.k_proj: 24.75
  encoder_attn.out_proj: 544.5
  encoder_attn.q_proj: 0.75732421875
  encoder_attn.v_proj: 26.046875
  fc1: 5.28515625
  fc2: 0.2406005859375
  self_attn.k_proj: 1.1787109375
  self_attn.out_proj: 0.298095703125
  self_attn.q_proj: 1.13671875
  self_attn.v_proj: 0.6494140625
- encoder_attn.k_proj: 26.109375
  encoder_attn.out_proj: 629.0
  encoder_attn.q_proj: 0.75927734375
  encoder_attn.v_proj: 24.859375
  fc1: 5.87890625
  fc2: 0.25
  self_attn.k_proj: 1.02734375
  self_attn.out_proj: 0.46630859375
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 25.25
  encoder_attn.out_proj: 528.0
  encoder_attn.q_proj: 0.7607421875
  encoder_attn.v_proj: 23.46875
  fc1: 6.0625
  fc2: 0.24609375
  self_attn.k_proj: 1.3017578125
  self_attn.out_proj: 0.24755859375
  self_attn.q_proj: 1.2578125
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 26.71875
  encoder_attn.out_proj: 708.0
  encoder_attn.q_proj: 0.78369140625
  encoder_attn.v_proj: 25.265625
  fc1: 5.53125
  fc2: 0.27197265625
  self_attn.k_proj: 1.6435546875
  self_attn.out_proj: 0.36279296875
  self_attn.q_proj: 1.302734375
  self_attn.v_proj: 0.7470703125
- encoder_attn.k_proj: 28.015625
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 0.5498046875
  encoder_attn.v_proj: 26.734375
  fc1: 4.46484375
  fc2: 0.361572265625
  self_attn.k_proj: 1.1123046875
  self_attn.out_proj: 0.276611328125
  self_attn.q_proj: 1.08203125
  self_attn.v_proj: 0.377197265625
encoder:
- fc1: 4.7109375
  fc2: 0.11865234375
  self_attn.k_proj: 4.74609375
  self_attn.out_proj: 0.1744384765625
  self_attn.q_proj: 4.4453125
  self_attn.v_proj: 3.412109375
- fc1: 2.2578125
  fc2: 0.022735595703125
  self_attn.k_proj: 0.669921875
  self_attn.out_proj: 0.08050537109375
  self_attn.q_proj: 0.55908203125
  self_attn.v_proj: 0.3095703125
- fc1: 2.568359375
  fc2: 0.00882720947265625
  self_attn.k_proj: 0.478515625
  self_attn.out_proj: 0.08551025390625
  self_attn.q_proj: 0.440185546875
  self_attn.v_proj: 0.299072265625
- fc1: 2.841796875
  fc2: 0.01190185546875
  self_attn.k_proj: 0.5615234375
  self_attn.out_proj: 0.069580078125
  self_attn.q_proj: 0.4521484375
  self_attn.v_proj: 0.32568359375
- fc1: 2.3359375
  fc2: 0.007843017578125
  self_attn.k_proj: 0.55908203125
  self_attn.out_proj: 0.066162109375
  self_attn.q_proj: 0.4443359375
  self_attn.v_proj: 0.327880859375
- fc1: 2.728515625
  fc2: 0.00908660888671875
  self_attn.k_proj: 0.52685546875
  self_attn.out_proj: 0.08599853515625
  self_attn.q_proj: 0.391357421875
  self_attn.v_proj: 0.35009765625
- fc1: 3.029296875
  fc2: 0.00933837890625
  self_attn.k_proj: 0.77685546875
  self_attn.out_proj: 0.07904052734375
  self_attn.q_proj: 0.58154296875
  self_attn.v_proj: 0.441650390625
- fc1: 3.439453125
  fc2: 0.013885498046875
  self_attn.k_proj: 0.84228515625
  self_attn.out_proj: 0.09033203125
  self_attn.q_proj: 0.65869140625
  self_attn.v_proj: 0.456787109375
- fc1: 3.23828125
  fc2: 0.023040771484375
  self_attn.k_proj: 0.89111328125
  self_attn.out_proj: 0.10394287109375
  self_attn.q_proj: 0.68798828125
  self_attn.v_proj: 0.478515625
- fc1: .inf
  fc2: 0.018585205078125
  self_attn.k_proj: 0.8564453125
  self_attn.out_proj: 0.084228515625
  self_attn.q_proj: 0.640625
  self_attn.v_proj: 0.479248046875
- fc1: 4.6484375
  fc2: 0.021820068359375
  self_attn.k_proj: 0.70751953125
  self_attn.out_proj: 0.10638427734375
  self_attn.q_proj: 0.56689453125
  self_attn.v_proj: 0.482421875
- fc1: 4.2890625
  fc2: 0.0208740234375
  self_attn.k_proj: 0.84814453125
  self_attn.out_proj: 0.09857177734375
  self_attn.q_proj: 0.611328125
  self_attn.v_proj: 0.4755859375
- fc1: 4.28515625
  fc2: 0.03265380859375
  self_attn.k_proj: 0.84716796875
  self_attn.out_proj: 0.09918212890625
  self_attn.q_proj: 0.6044921875
  self_attn.v_proj: 0.44287109375
- fc1: 3.583984375
  fc2: 0.047821044921875
  self_attn.k_proj: 0.7451171875
  self_attn.out_proj: 0.10833740234375
  self_attn.q_proj: 0.5556640625
  self_attn.v_proj: 0.416748046875
- fc1: .inf
  fc2: 0.05987548828125
  self_attn.k_proj: 0.857421875
  self_attn.out_proj: 0.0765380859375
  self_attn.q_proj: 0.63330078125
  self_attn.v_proj: 0.416259765625
- fc1: .inf
  fc2: 0.0567626953125
  self_attn.k_proj: 0.779296875
  self_attn.out_proj: 0.09124755859375
  self_attn.q_proj: 0.60205078125
  self_attn.v_proj: 0.416259765625
- fc1: .inf
  fc2: 0.0682373046875
  self_attn.k_proj: 0.70556640625
  self_attn.out_proj: 0.10992431640625
  self_attn.q_proj: 0.5068359375
  self_attn.v_proj: 0.414794921875
- fc1: .inf
  fc2: 0.083251953125
  self_attn.k_proj: 0.93701171875
  self_attn.out_proj: 0.10986328125
  self_attn.q_proj: 0.74658203125
  self_attn.v_proj: 0.51806640625
- fc1: .inf
  fc2: 0.10858154296875
  self_attn.k_proj: 0.9482421875
  self_attn.out_proj: 0.12548828125
  self_attn.q_proj: 0.7060546875
  self_attn.v_proj: 0.5146484375
- fc1: .inf
  fc2: 0.1317138671875
  self_attn.k_proj: 0.921875
  self_attn.out_proj: 0.1240234375
  self_attn.q_proj: 0.78173828125
  self_attn.v_proj: 0.52001953125
- fc1: .inf
  fc2: 0.181640625
  self_attn.k_proj: 1.095703125
  self_attn.out_proj: 0.1785888671875
  self_attn.q_proj: 1.0625
  self_attn.v_proj: 0.62646484375
- fc1: .inf
  fc2: 0.166015625
  self_attn.k_proj: 1.1142578125
  self_attn.out_proj: 0.1534423828125
  self_attn.q_proj: 1.0595703125
  self_attn.v_proj: 0.642578125
- fc1: .inf
  fc2: 0.171630859375
  self_attn.k_proj: 1.3076171875
  self_attn.out_proj: 0.2052001953125
  self_attn.q_proj: 1.322265625
  self_attn.v_proj: 0.7236328125
- fc1: .inf
  fc2: 0.18505859375
  self_attn.k_proj: 1.265625
  self_attn.out_proj: 0.1728515625
  self_attn.q_proj: 1.298828125
  self_attn.v_proj: 0.74609375
- fc1: .inf
  fc2: 0.197998046875
  self_attn.k_proj: 1.52734375
  self_attn.out_proj: 0.2115478515625
  self_attn.q_proj: 1.4501953125
  self_attn.v_proj: 0.84033203125
- fc1: .inf
  fc2: 0.2254638671875
  self_attn.k_proj: 1.462890625
  self_attn.out_proj: 0.18701171875
  self_attn.q_proj: 1.421875
  self_attn.v_proj: 0.8125
- fc1: .inf
  fc2: 0.2354736328125
  self_attn.k_proj: 1.6064453125
  self_attn.out_proj: 0.1903076171875
  self_attn.q_proj: 1.5546875
  self_attn.v_proj: 0.8984375
- fc1: .inf
  fc2: 0.2607421875
  self_attn.k_proj: 1.53515625
  self_attn.out_proj: 0.14208984375
  self_attn.q_proj: 1.365234375
  self_attn.v_proj: 0.8408203125
- fc1: .inf
  fc2: 0.298095703125
  self_attn.k_proj: 1.6630859375
  self_attn.out_proj: 0.235595703125
  self_attn.q_proj: 1.5537109375
  self_attn.v_proj: 0.98974609375
- fc1: .inf
  fc2: 0.32958984375
  self_attn.k_proj: 1.888671875
  self_attn.out_proj: 0.31298828125
  self_attn.q_proj: 1.8701171875
  self_attn.v_proj: 1.1865234375
- fc1: .inf
  fc2: 0.38330078125
  self_attn.k_proj: 1.8525390625
  self_attn.out_proj: 0.328369140625
  self_attn.q_proj: 1.861328125
  self_attn.v_proj: 1.23046875
- fc1: .inf
  fc2: 0.417236328125
  self_attn.k_proj: 1.9345703125
  self_attn.out_proj: 0.3759765625
  self_attn.q_proj: 2.013671875
  self_attn.v_proj: 1.30859375
