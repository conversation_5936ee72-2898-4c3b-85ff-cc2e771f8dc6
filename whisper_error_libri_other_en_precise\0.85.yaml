decoder:
- encoder_attn.k_proj: 13.7578125
  encoder_attn.out_proj: 354.0
  encoder_attn.q_proj: 5.44921875
  encoder_attn.v_proj: 13.34375
  fc1: 6.48046875
  fc2: 4.515625
  self_attn.k_proj: 27.0
  self_attn.out_proj: 2.5546875
  self_attn.q_proj: 22.71875
  self_attn.v_proj: 7.92578125
- encoder_attn.k_proj: 15.015625
  encoder_attn.out_proj: 136.0
  encoder_attn.q_proj: 0.8232421875
  encoder_attn.v_proj: 14.015625
  fc1: 5.84375
  fc2: 1.2666015625
  self_attn.k_proj: 1.9111328125
  self_attn.out_proj: 0.047332763671875
  self_attn.q_proj: 1.669921875
  self_attn.v_proj: 0.3515625
- encoder_attn.k_proj: 8.828125
  encoder_attn.out_proj: 31.015625
  encoder_attn.q_proj: 0.7177734375
  encoder_attn.v_proj: 8.2421875
  fc1: 10.2578125
  fc2: 0.537109375
  self_attn.k_proj: 0.49267578125
  self_attn.out_proj: 0.042022705078125
  self_attn.q_proj: 0.9228515625
  self_attn.v_proj: 0.19677734375
- encoder_attn.k_proj: 7.2421875
  encoder_attn.out_proj: 46.125
  encoder_attn.q_proj: 0.050048828125
  encoder_attn.v_proj: 5.69921875
  fc1: 5.36328125
  fc2: 0.99365234375
  self_attn.k_proj: 0.2049560546875
  self_attn.out_proj: 0.03125
  self_attn.q_proj: 0.361572265625
  self_attn.v_proj: 0.184326171875
- encoder_attn.k_proj: 14.1171875
  encoder_attn.out_proj: 126.5625
  encoder_attn.q_proj: 0.6474609375
  encoder_attn.v_proj: 12.765625
  fc1: 5.11328125
  fc2: 0.8984375
  self_attn.k_proj: 0.3271484375
  self_attn.out_proj: 0.033447265625
  self_attn.q_proj: 0.8701171875
  self_attn.v_proj: 0.259521484375
- encoder_attn.k_proj: 11.921875
  encoder_attn.out_proj: 183.875
  encoder_attn.q_proj: 0.79443359375
  encoder_attn.v_proj: 10.40625
  fc1: 10.0
  fc2: 0.17578125
  self_attn.k_proj: 0.63525390625
  self_attn.out_proj: 0.26123046875
  self_attn.q_proj: 0.9658203125
  self_attn.v_proj: 0.33984375
- encoder_attn.k_proj: 19.78125
  encoder_attn.out_proj: 171.0
  encoder_attn.q_proj: 1.0771484375
  encoder_attn.v_proj: 17.125
  fc1: 11.7578125
  fc2: 0.19970703125
  self_attn.k_proj: 0.97900390625
  self_attn.out_proj: 0.25146484375
  self_attn.q_proj: 1.1279296875
  self_attn.v_proj: 0.410400390625
- encoder_attn.k_proj: 15.015625
  encoder_attn.out_proj: 234.25
  encoder_attn.q_proj: 1.490234375
  encoder_attn.v_proj: 14.1640625
  fc1: 11.8046875
  fc2: 0.1353759765625
  self_attn.k_proj: 1.2294921875
  self_attn.out_proj: 0.4228515625
  self_attn.q_proj: 1.0673828125
  self_attn.v_proj: 0.4150390625
- encoder_attn.k_proj: 12.1875
  encoder_attn.out_proj: 133.0
  encoder_attn.q_proj: 1.927734375
  encoder_attn.v_proj: 9.453125
  fc1: 22.75
  fc2: 0.1064453125
  self_attn.k_proj: 1.4931640625
  self_attn.out_proj: 0.319091796875
  self_attn.q_proj: 1.291015625
  self_attn.v_proj: 0.4580078125
- encoder_attn.k_proj: 12.7421875
  encoder_attn.out_proj: 341.25
  encoder_attn.q_proj: 2.16796875
  encoder_attn.v_proj: 12.8515625
  fc1: 20.828125
  fc2: 0.1617431640625
  self_attn.k_proj: 1.3095703125
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.3125
  self_attn.v_proj: 0.414794921875
- encoder_attn.k_proj: 19.28125
  encoder_attn.out_proj: 175.875
  encoder_attn.q_proj: 1.5546875
  encoder_attn.v_proj: 13.1328125
  fc1: 21.1875
  fc2: 0.11871337890625
  self_attn.k_proj: 2.279296875
  self_attn.out_proj: 0.36181640625
  self_attn.q_proj: 1.8203125
  self_attn.v_proj: 0.64208984375
- encoder_attn.k_proj: 19.015625
  encoder_attn.out_proj: 377.75
  encoder_attn.q_proj: 0.6552734375
  encoder_attn.v_proj: 16.0
  fc1: 18.203125
  fc2: 0.12493896484375
  self_attn.k_proj: 1.5185546875
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.5107421875
  self_attn.v_proj: 0.525390625
- encoder_attn.k_proj: 20.28125
  encoder_attn.out_proj: 140.25
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 18.9375
  fc1: 12.6015625
  fc2: 0.2071533203125
  self_attn.k_proj: 2.111328125
  self_attn.out_proj: 0.61328125
  self_attn.q_proj: 1.9716796875
  self_attn.v_proj: 0.6826171875
- encoder_attn.k_proj: 23.09375
  encoder_attn.out_proj: 434.5
  encoder_attn.q_proj: 1.0400390625
  encoder_attn.v_proj: 18.5625
  fc1: 9.3203125
  fc2: 0.481201171875
  self_attn.k_proj: 2.0
  self_attn.out_proj: 0.453369140625
  self_attn.q_proj: 1.5517578125
  self_attn.v_proj: 0.80517578125
- encoder_attn.k_proj: 22.34375
  encoder_attn.out_proj: 296.0
  encoder_attn.q_proj: 0.5810546875
  encoder_attn.v_proj: 19.640625
  fc1: 10.4296875
  fc2: 0.2451171875
  self_attn.k_proj: 1.796875
  self_attn.out_proj: 0.3662109375
  self_attn.q_proj: 1.86328125
  self_attn.v_proj: 0.697265625
- encoder_attn.k_proj: 24.109375
  encoder_attn.out_proj: 328.75
  encoder_attn.q_proj: 0.771484375
  encoder_attn.v_proj: 20.703125
  fc1: 9.6953125
  fc2: 0.32177734375
  self_attn.k_proj: 2.0
  self_attn.out_proj: 0.56494140625
  self_attn.q_proj: 2.0
  self_attn.v_proj: 0.9609375
- encoder_attn.k_proj: 24.828125
  encoder_attn.out_proj: 708.5
  encoder_attn.q_proj: 0.7587890625
  encoder_attn.v_proj: 22.328125
  fc1: 10.046875
  fc2: 0.26416015625
  self_attn.k_proj: 1.8515625
  self_attn.out_proj: 0.383544921875
  self_attn.q_proj: 1.7099609375
  self_attn.v_proj: 0.88720703125
- encoder_attn.k_proj: 24.125
  encoder_attn.out_proj: 480.5
  encoder_attn.q_proj: 0.98291015625
  encoder_attn.v_proj: 21.3125
  fc1: 8.9140625
  fc2: 0.25
  self_attn.k_proj: 1.4736328125
  self_attn.out_proj: 0.70166015625
  self_attn.q_proj: 1.53125
  self_attn.v_proj: 0.9326171875
- encoder_attn.k_proj: 24.171875
  encoder_attn.out_proj: 759.5
  encoder_attn.q_proj: 1.599609375
  encoder_attn.v_proj: 22.40625
  fc1: 9.21875
  fc2: 0.2239990234375
  self_attn.k_proj: 1.9482421875
  self_attn.out_proj: 0.39013671875
  self_attn.q_proj: 1.69921875
  self_attn.v_proj: 0.984375
- encoder_attn.k_proj: 25.625
  encoder_attn.out_proj: 593.5
  encoder_attn.q_proj: 1.302734375
  encoder_attn.v_proj: 24.609375
  fc1: 11.4296875
  fc2: 0.26318359375
  self_attn.k_proj: 1.4853515625
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.3271484375
  self_attn.v_proj: 0.63134765625
- encoder_attn.k_proj: 26.40625
  encoder_attn.out_proj: 813.5
  encoder_attn.q_proj: 1.763671875
  encoder_attn.v_proj: 23.953125
  fc1: 8.7109375
  fc2: 0.291015625
  self_attn.k_proj: 2.3359375
  self_attn.out_proj: 0.5
  self_attn.q_proj: 1.7587890625
  self_attn.v_proj: 0.85302734375
- encoder_attn.k_proj: 26.234375
  encoder_attn.out_proj: 531.0
  encoder_attn.q_proj: 1.3515625
  encoder_attn.v_proj: 23.5
  fc1: 9.953125
  fc2: 0.265625
  self_attn.k_proj: 1.3984375
  self_attn.out_proj: 0.810546875
  self_attn.q_proj: 1.3955078125
  self_attn.v_proj: 0.71728515625
- encoder_attn.k_proj: 25.59375
  encoder_attn.out_proj: 572.5
  encoder_attn.q_proj: 1.9970703125
  encoder_attn.v_proj: 27.75
  fc1: 7.0703125
  fc2: 0.136962890625
  self_attn.k_proj: 2.486328125
  self_attn.out_proj: 0.29296875
  self_attn.q_proj: 1.8681640625
  self_attn.v_proj: 0.97119140625
- encoder_attn.k_proj: 27.890625
  encoder_attn.out_proj: 684.5
  encoder_attn.q_proj: 1.4755859375
  encoder_attn.v_proj: 25.15625
  fc1: 5.97265625
  fc2: 0.31396484375
  self_attn.k_proj: 1.4736328125
  self_attn.out_proj: 0.36376953125
  self_attn.q_proj: 1.3837890625
  self_attn.v_proj: 0.6748046875
- encoder_attn.k_proj: 26.046875
  encoder_attn.out_proj: 540.5
  encoder_attn.q_proj: 1.2685546875
  encoder_attn.v_proj: 24.1875
  fc1: 5.06640625
  fc2: 0.3056640625
  self_attn.k_proj: 1.4189453125
  self_attn.out_proj: 0.6171875
  self_attn.q_proj: 1.3017578125
  self_attn.v_proj: 0.5771484375
- encoder_attn.k_proj: 27.890625
  encoder_attn.out_proj: 771.0
  encoder_attn.q_proj: 1.130859375
  encoder_attn.v_proj: 25.703125
  fc1: 6.53125
  fc2: 0.316162109375
  self_attn.k_proj: 1.48828125
  self_attn.out_proj: 0.369140625
  self_attn.q_proj: 1.3564453125
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 29.5625
  encoder_attn.out_proj: 878.5
  encoder_attn.q_proj: 1.1689453125
  encoder_attn.v_proj: 23.421875
  fc1: 5.9609375
  fc2: 0.25
  self_attn.k_proj: 1.5732421875
  self_attn.out_proj: 0.283935546875
  self_attn.q_proj: 1.4072265625
  self_attn.v_proj: 0.68603515625
- encoder_attn.k_proj: 28.984375
  encoder_attn.out_proj: 628.0
  encoder_attn.q_proj: 0.98583984375
  encoder_attn.v_proj: 27.40625
  fc1: 7.15234375
  fc2: 0.283447265625
  self_attn.k_proj: 1.4677734375
  self_attn.out_proj: 0.369384765625
  self_attn.q_proj: 1.453125
  self_attn.v_proj: 0.7548828125
- encoder_attn.k_proj: 29.984375
  encoder_attn.out_proj: 685.0
  encoder_attn.q_proj: 0.9306640625
  encoder_attn.v_proj: 25.78125
  fc1: 8.0
  fc2: 0.310791015625
  self_attn.k_proj: 1.427734375
  self_attn.out_proj: 0.642578125
  self_attn.q_proj: 1.40625
  self_attn.v_proj: 0.64599609375
- encoder_attn.k_proj: 29.28125
  encoder_attn.out_proj: 577.5
  encoder_attn.q_proj: 0.99951171875
  encoder_attn.v_proj: 24.46875
  fc1: 8.2734375
  fc2: 0.313232421875
  self_attn.k_proj: 1.8037109375
  self_attn.out_proj: 0.322509765625
  self_attn.q_proj: 1.5185546875
  self_attn.v_proj: 0.67431640625
- encoder_attn.k_proj: 31.609375
  encoder_attn.out_proj: 794.0
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 26.328125
  fc1: 7.30859375
  fc2: 0.350830078125
  self_attn.k_proj: 1.9736328125
  self_attn.out_proj: 0.474609375
  self_attn.q_proj: 1.751953125
  self_attn.v_proj: 0.96826171875
- encoder_attn.k_proj: 32.9375
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 0.73291015625
  encoder_attn.v_proj: 28.4375
  fc1: 5.796875
  fc2: 0.5
  self_attn.k_proj: 1.580078125
  self_attn.out_proj: 0.388427734375
  self_attn.q_proj: 1.6494140625
  self_attn.v_proj: 0.494384765625
encoder:
- fc1: .inf
  fc2: 0.1636962890625
  self_attn.k_proj: .inf
  self_attn.out_proj: 0.2327880859375
  self_attn.q_proj: 4.59375
  self_attn.v_proj: 3.083984375
- fc1: 2.83203125
  fc2: 0.030792236328125
  self_attn.k_proj: 0.8271484375
  self_attn.out_proj: 0.10028076171875
  self_attn.q_proj: 0.69482421875
  self_attn.v_proj: 0.37353515625
- fc1: 3.251953125
  fc2: 0.01055145263671875
  self_attn.k_proj: 0.5986328125
  self_attn.out_proj: 0.10540771484375
  self_attn.q_proj: 0.5390625
  self_attn.v_proj: 0.355224609375
- fc1: 3.71484375
  fc2: 0.0153656005859375
  self_attn.k_proj: 0.6875
  self_attn.out_proj: 0.0831298828125
  self_attn.q_proj: 0.54541015625
  self_attn.v_proj: 0.3818359375
- fc1: 2.865234375
  fc2: 0.01058197021484375
  self_attn.k_proj: 0.69287109375
  self_attn.out_proj: 0.08013916015625
  self_attn.q_proj: 0.53076171875
  self_attn.v_proj: 0.388427734375
- fc1: 3.400390625
  fc2: 0.0120697021484375
  self_attn.k_proj: 0.64892578125
  self_attn.out_proj: 0.10150146484375
  self_attn.q_proj: 0.46923828125
  self_attn.v_proj: 0.41455078125
- fc1: 3.76953125
  fc2: 0.01296234130859375
  self_attn.k_proj: 0.9462890625
  self_attn.out_proj: 0.093505859375
  self_attn.q_proj: 0.69580078125
  self_attn.v_proj: 0.5068359375
- fc1: 4.24609375
  fc2: 0.0195159912109375
  self_attn.k_proj: 1.037109375
  self_attn.out_proj: 0.105712890625
  self_attn.q_proj: 0.78515625
  self_attn.v_proj: 0.53125
- fc1: 4.0234375
  fc2: 0.030364990234375
  self_attn.k_proj: 1.0791015625
  self_attn.out_proj: 0.11962890625
  self_attn.q_proj: 0.8173828125
  self_attn.v_proj: 0.552734375
- fc1: .inf
  fc2: 0.0248260498046875
  self_attn.k_proj: 1.02734375
  self_attn.out_proj: 0.09771728515625
  self_attn.q_proj: 0.75830078125
  self_attn.v_proj: 0.55126953125
- fc1: .inf
  fc2: 0.0285797119140625
  self_attn.k_proj: 0.8525390625
  self_attn.out_proj: 0.12384033203125
  self_attn.q_proj: 0.67724609375
  self_attn.v_proj: 0.5537109375
- fc1: .inf
  fc2: 0.027435302734375
  self_attn.k_proj: 1.015625
  self_attn.out_proj: 0.11517333984375
  self_attn.q_proj: 0.72021484375
  self_attn.v_proj: 0.55078125
- fc1: .inf
  fc2: 0.042083740234375
  self_attn.k_proj: 1.0234375
  self_attn.out_proj: 0.11456298828125
  self_attn.q_proj: 0.7041015625
  self_attn.v_proj: 0.5068359375
- fc1: 4.14453125
  fc2: 0.058563232421875
  self_attn.k_proj: 0.89306640625
  self_attn.out_proj: 0.1260986328125
  self_attn.q_proj: 0.6552734375
  self_attn.v_proj: 0.473388671875
- fc1: .inf
  fc2: 0.07427978515625
  self_attn.k_proj: 1.0263671875
  self_attn.out_proj: 0.08941650390625
  self_attn.q_proj: 0.74658203125
  self_attn.v_proj: 0.471923828125
- fc1: .inf
  fc2: 0.06976318359375
  self_attn.k_proj: 0.9384765625
  self_attn.out_proj: 0.11029052734375
  self_attn.q_proj: 0.70947265625
  self_attn.v_proj: 0.472900390625
- fc1: .inf
  fc2: 0.08343505859375
  self_attn.k_proj: 0.8583984375
  self_attn.out_proj: 0.1279296875
  self_attn.q_proj: 0.595703125
  self_attn.v_proj: 0.479248046875
- fc1: .inf
  fc2: 0.10064697265625
  self_attn.k_proj: 1.1318359375
  self_attn.out_proj: 0.1260986328125
  self_attn.q_proj: 0.87890625
  self_attn.v_proj: 0.59375
- fc1: .inf
  fc2: 0.1304931640625
  self_attn.k_proj: 1.1533203125
  self_attn.out_proj: 0.1453857421875
  self_attn.q_proj: 0.8310546875
  self_attn.v_proj: 0.59228515625
- fc1: .inf
  fc2: 0.156494140625
  self_attn.k_proj: 1.1103515625
  self_attn.out_proj: 0.1424560546875
  self_attn.q_proj: 0.91650390625
  self_attn.v_proj: 0.59619140625
- fc1: .inf
  fc2: 0.205810546875
  self_attn.k_proj: 1.298828125
  self_attn.out_proj: 0.208251953125
  self_attn.q_proj: 1.24609375
  self_attn.v_proj: 0.71142578125
- fc1: .inf
  fc2: 0.193359375
  self_attn.k_proj: 1.32421875
  self_attn.out_proj: 0.175048828125
  self_attn.q_proj: 1.23828125
  self_attn.v_proj: 0.72412109375
- fc1: .inf
  fc2: 0.200439453125
  self_attn.k_proj: 1.5390625
  self_attn.out_proj: 0.2333984375
  self_attn.q_proj: 1.548828125
  self_attn.v_proj: 0.81640625
- fc1: .inf
  fc2: 0.2115478515625
  self_attn.k_proj: 1.4873046875
  self_attn.out_proj: 0.1995849609375
  self_attn.q_proj: 1.5205078125
  self_attn.v_proj: 0.84326171875
- fc1: .inf
  fc2: 0.2281494140625
  self_attn.k_proj: 1.7880859375
  self_attn.out_proj: 0.2373046875
  self_attn.q_proj: 1.6904296875
  self_attn.v_proj: 0.947265625
- fc1: .inf
  fc2: 0.25537109375
  self_attn.k_proj: 1.708984375
  self_attn.out_proj: 0.2178955078125
  self_attn.q_proj: 1.650390625
  self_attn.v_proj: 0.91162109375
- fc1: .inf
  fc2: 0.26611328125
  self_attn.k_proj: 1.8583984375
  self_attn.out_proj: 0.21337890625
  self_attn.q_proj: 1.791015625
  self_attn.v_proj: 1.0
- fc1: .inf
  fc2: 0.294189453125
  self_attn.k_proj: 1.7724609375
  self_attn.out_proj: 0.1632080078125
  self_attn.q_proj: 1.5771484375
  self_attn.v_proj: 0.94091796875
- fc1: .inf
  fc2: 0.340576171875
  self_attn.k_proj: 1.912109375
  self_attn.out_proj: 0.270751953125
  self_attn.q_proj: 1.775390625
  self_attn.v_proj: 1.1064453125
- fc1: .inf
  fc2: 0.373779296875
  self_attn.k_proj: 2.185546875
  self_attn.out_proj: 0.3544921875
  self_attn.q_proj: 2.154296875
  self_attn.v_proj: 1.337890625
- fc1: .inf
  fc2: 0.4365234375
  self_attn.k_proj: 2.12890625
  self_attn.out_proj: 0.3701171875
  self_attn.q_proj: 2.134765625
  self_attn.v_proj: 1.3828125
- fc1: .inf
  fc2: 0.47900390625
  self_attn.k_proj: 2.23046875
  self_attn.out_proj: 0.421142578125
  self_attn.q_proj: 2.30078125
  self_attn.v_proj: 1.482421875
