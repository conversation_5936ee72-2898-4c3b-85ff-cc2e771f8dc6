decoder:
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.75
  fc2: 0.45
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.36
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.75
  fc2: 0.6
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.63
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.56
  fc1: 0.72
  fc2: 0.73
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.74
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.63
  fc1: 0.74
  fc2: 0.7
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.57
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.52
  fc1: 0.75
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.58
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.51
  fc1: 0.67
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.75
  self_attn.k_proj: 0.73
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.72
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.66
  fc2: 0.75
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.62
  self_attn.q_proj: 0.68
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.55
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.52
  fc1: 0.62
  fc2: 0.75
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.63
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.59
  fc2: 0.75
  self_attn.k_proj: 0.66
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.67
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.6
  fc2: 0.75
  self_attn.k_proj: 0.57
  self_attn.out_proj: 0.53
  self_attn.q_proj: 0.58
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.62
  fc2: 0.75
  self_attn.k_proj: 0.63
  self_attn.out_proj: 0.55
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.75
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.65
  fc2: 0.75
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.46
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.69
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.68
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.42
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.66
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.7
  self_attn.k_proj: 0.55
  self_attn.out_proj: 0.47
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.67
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.41
  self_attn.q_proj: 0.45
  self_attn.v_proj: 0.63
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.67
  self_attn.k_proj: 0.51
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.67
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.54
  self_attn.v_proj: 0.68
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.66
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.41
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.63
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.68
  fc2: 0.65
  self_attn.k_proj: 0.56
  self_attn.out_proj: 0.46
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.71
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.69
  fc2: 0.65
  self_attn.k_proj: 0.55
  self_attn.out_proj: 0.47
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.65
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.68
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.71
  fc2: 0.52
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.45
  self_attn.q_proj: 0.55
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.71
  fc2: 0.53
  self_attn.k_proj: 0.54
  self_attn.out_proj: 0.46
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.71
  fc2: 0.64
  self_attn.k_proj: 0.52
  self_attn.out_proj: 0.46
  self_attn.q_proj: 0.52
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.7
  fc2: 0.62
  self_attn.k_proj: 0.47
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.47
  self_attn.v_proj: 0.61
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.69
  fc2: 0.62
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.49
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.68
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.67
  fc2: 0.6
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.45
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.64
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.64
  fc2: 0.6
  self_attn.k_proj: 0.51
  self_attn.out_proj: 0.47
  self_attn.q_proj: 0.54
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.63
  fc2: 0.61
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.48
  self_attn.q_proj: 0.56
  self_attn.v_proj: 0.67
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.64
  fc2: 0.62
  self_attn.k_proj: 0.43
  self_attn.out_proj: 0.44
  self_attn.q_proj: 0.47
  self_attn.v_proj: 0.6
- encoder_attn.k_proj: 0.5
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.75
  encoder_attn.v_proj: 0.5
  fc1: 0.72
  fc2: 0.63
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.75
encoder:
- fc1: 0.5
  fc2: 0.5
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.5
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.5
- fc1: 0.67
  fc2: 0.75
  self_attn.k_proj: 0.74
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.65
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.66
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.75
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.66
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.74
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.64
  fc2: 0.75
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.62
  fc2: 0.75
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.62
  fc2: 0.75
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.66
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.75
- fc1: 0.59
  fc2: 0.75
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.72
  self_attn.v_proj: 0.75
- fc1: 0.57
  fc2: 0.75
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.75
- fc1: 0.54
  fc2: 0.75
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.57
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.55
  fc2: 0.75
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.75
- fc1: 0.55
  fc2: 0.75
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.63
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.54
  fc2: 0.75
  self_attn.k_proj: 0.71
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.51
  fc2: 0.75
  self_attn.k_proj: 0.68
  self_attn.out_proj: 0.68
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.75
  self_attn.k_proj: 0.69
  self_attn.out_proj: 0.57
  self_attn.q_proj: 0.74
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.75
  self_attn.k_proj: 0.72
  self_attn.out_proj: 0.6
  self_attn.q_proj: 0.75
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.74
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.58
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.7
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.58
  self_attn.q_proj: 0.71
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.67
  self_attn.out_proj: 0.58
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.75
- fc1: 0.5
  fc2: 0.37
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.47
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.74
- fc1: 0.5
  fc2: 0.58
  self_attn.k_proj: 0.62
  self_attn.out_proj: 0.51
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.74
- fc1: 0.5
  fc2: 0.57
  self_attn.k_proj: 0.57
  self_attn.out_proj: 0.42
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.7
- fc1: 0.5
  fc2: 0.53
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.42
  self_attn.q_proj: 0.57
  self_attn.v_proj: 0.69
- fc1: 0.5
  fc2: 0.51
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.37
  self_attn.q_proj: 0.54
  self_attn.v_proj: 0.66
- fc1: 0.5
  fc2: 0.46
  self_attn.k_proj: 0.53
  self_attn.out_proj: 0.41
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.44
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.4
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.62
- fc1: 0.5
  fc2: 0.41
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.48
  self_attn.q_proj: 0.53
  self_attn.v_proj: 0.64
- fc1: 0.5
  fc2: 0.39
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.35
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.58
- fc1: 0.5
  fc2: 0.37
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.3
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.53
- fc1: 0.5
  fc2: 0.34
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.3
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.52
- fc1: 0.5
  fc2: 0.33
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.3
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.51
