decoder:
- encoder_attn.k_proj: 18.96875
  encoder_attn.out_proj: 449.25
  encoder_attn.q_proj: 9.1953125
  encoder_attn.v_proj: 14.828125
  fc1: 42.78125
  fc2: 11.1796875
  self_attn.k_proj: 38.71875
  self_attn.out_proj: 3.72265625
  self_attn.q_proj: 32.65625
  self_attn.v_proj: 9.8515625
- encoder_attn.k_proj: 22.046875
  encoder_attn.out_proj: 182.25
  encoder_attn.q_proj: 1.482421875
  encoder_attn.v_proj: 15.53125
  fc1: 10.5703125
  fc2: 2.9609375
  self_attn.k_proj: 3.693359375
  self_attn.out_proj: 0.0982666015625
  self_attn.q_proj: 3.8515625
  self_attn.v_proj: 0.81884765625
- encoder_attn.k_proj: 12.0234375
  encoder_attn.out_proj: 48.5
  encoder_attn.q_proj: 1.041015625
  encoder_attn.v_proj: 9.546875
  fc1: 25.03125
  fc2: 1.20703125
  self_attn.k_proj: 1.0859375
  self_attn.out_proj: 0.0889892578125
  self_attn.q_proj: 2.20703125
  self_attn.v_proj: 0.391845703125
- encoder_attn.k_proj: 10.265625
  encoder_attn.out_proj: 93.3125
  encoder_attn.q_proj: 0.093017578125
  encoder_attn.v_proj: 6.66015625
  fc1: 11.640625
  fc2: 2.01171875
  self_attn.k_proj: 0.342529296875
  self_attn.out_proj: 0.10552978515625
  self_attn.q_proj: 0.7177734375
  self_attn.v_proj: 0.350341796875
- encoder_attn.k_proj: 19.265625
  encoder_attn.out_proj: 171.375
  encoder_attn.q_proj: 1.3466796875
  encoder_attn.v_proj: 15.1953125
  fc1: 10.7578125
  fc2: 1.958984375
  self_attn.k_proj: 0.71875
  self_attn.out_proj: 0.1343994140625
  self_attn.q_proj: 2.537109375
  self_attn.v_proj: 0.462890625
- encoder_attn.k_proj: 17.109375
  encoder_attn.out_proj: 249.375
  encoder_attn.q_proj: 1.1572265625
  encoder_attn.v_proj: 11.6796875
  fc1: 22.125
  fc2: 0.41943359375
  self_attn.k_proj: 1.0048828125
  self_attn.out_proj: 0.60009765625
  self_attn.q_proj: 1.62109375
  self_attn.v_proj: 0.5927734375
- encoder_attn.k_proj: 27.734375
  encoder_attn.out_proj: 252.5
  encoder_attn.q_proj: 2.21484375
  encoder_attn.v_proj: 19.75
  fc1: 26.5
  fc2: 0.408203125
  self_attn.k_proj: 1.802734375
  self_attn.out_proj: 0.471923828125
  self_attn.q_proj: 2.0
  self_attn.v_proj: 0.7734375
- encoder_attn.k_proj: 21.046875
  encoder_attn.out_proj: 317.5
  encoder_attn.q_proj: 3.08984375
  encoder_attn.v_proj: 15.5859375
  fc1: 30.578125
  fc2: 0.311767578125
  self_attn.k_proj: 2.3203125
  self_attn.out_proj: 0.73291015625
  self_attn.q_proj: 2.279296875
  self_attn.v_proj: 0.7392578125
- encoder_attn.k_proj: 17.03125
  encoder_attn.out_proj: 194.75
  encoder_attn.q_proj: 2.173828125
  encoder_attn.v_proj: 10.6953125
  fc1: 60.03125
  fc2: 0.21630859375
  self_attn.k_proj: 2.880859375
  self_attn.out_proj: 0.62353515625
  self_attn.q_proj: 2.5390625
  self_attn.v_proj: 0.87109375
- encoder_attn.k_proj: 18.0625
  encoder_attn.out_proj: 445.25
  encoder_attn.q_proj: 3.787109375
  encoder_attn.v_proj: 14.234375
  fc1: 58.5
  fc2: 0.361328125
  self_attn.k_proj: 2.701171875
  self_attn.out_proj: 0.491943359375
  self_attn.q_proj: 2.783203125
  self_attn.v_proj: 0.72607421875
- encoder_attn.k_proj: 27.375
  encoder_attn.out_proj: 253.0
  encoder_attn.q_proj: 2.87109375
  encoder_attn.v_proj: 14.6640625
  fc1: 58.53125
  fc2: 0.1837158203125
  self_attn.k_proj: 4.0
  self_attn.out_proj: 0.705078125
  self_attn.q_proj: 3.82421875
  self_attn.v_proj: 1.1474609375
- encoder_attn.k_proj: 27.828125
  encoder_attn.out_proj: 504.25
  encoder_attn.q_proj: 1.3046875
  encoder_attn.v_proj: 17.53125
  fc1: 49.75
  fc2: 0.244384765625
  self_attn.k_proj: 3.166015625
  self_attn.out_proj: 0.481201171875
  self_attn.q_proj: 3.435546875
  self_attn.v_proj: 1.0
- encoder_attn.k_proj: 30.015625
  encoder_attn.out_proj: 189.5
  encoder_attn.q_proj: 2.49609375
  encoder_attn.v_proj: 20.953125
  fc1: 32.0
  fc2: 0.363525390625
  self_attn.k_proj: 4.0
  self_attn.out_proj: 1.220703125
  self_attn.q_proj: 4.0234375
  self_attn.v_proj: 1.5048828125
- encoder_attn.k_proj: 32.0
  encoder_attn.out_proj: 543.0
  encoder_attn.q_proj: 2.1796875
  encoder_attn.v_proj: 20.375
  fc1: 20.453125
  fc2: 0.95947265625
  self_attn.k_proj: 3.525390625
  self_attn.out_proj: 0.7392578125
  self_attn.q_proj: 3.458984375
  self_attn.v_proj: 1.478515625
- encoder_attn.k_proj: 32.0
  encoder_attn.out_proj: 390.0
  encoder_attn.q_proj: 1.419921875
  encoder_attn.v_proj: 21.5
  fc1: 24.21875
  fc2: 0.447509765625
  self_attn.k_proj: 3.4375
  self_attn.out_proj: 0.60595703125
  self_attn.q_proj: 3.576171875
  self_attn.v_proj: 1.314453125
- encoder_attn.k_proj: 33.6875
  encoder_attn.out_proj: 433.75
  encoder_attn.q_proj: 1.9052734375
  encoder_attn.v_proj: 22.5625
  fc1: 22.546875
  fc2: 0.61181640625
  self_attn.k_proj: 4.0
  self_attn.out_proj: 0.97802734375
  self_attn.q_proj: 4.359375
  self_attn.v_proj: 1.7705078125
- encoder_attn.k_proj: 36.0
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.353515625
  encoder_attn.v_proj: 24.78125
  fc1: 23.875
  fc2: 0.486328125
  self_attn.k_proj: 3.203125
  self_attn.out_proj: 0.75244140625
  self_attn.q_proj: 3.20703125
  self_attn.v_proj: 1.50390625
- encoder_attn.k_proj: 34.03125
  encoder_attn.out_proj: 602.5
  encoder_attn.q_proj: 1.9609375
  encoder_attn.v_proj: 23.421875
  fc1: 20.96875
  fc2: 0.408447265625
  self_attn.k_proj: 2.955078125
  self_attn.out_proj: 1.4091796875
  self_attn.q_proj: 3.005859375
  self_attn.v_proj: 1.765625
- encoder_attn.k_proj: 35.0
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 3.3515625
  encoder_attn.v_proj: 24.8125
  fc1: 22.3125
  fc2: 0.415283203125
  self_attn.k_proj: 3.892578125
  self_attn.out_proj: 0.67236328125
  self_attn.q_proj: 3.384765625
  self_attn.v_proj: 1.943359375
- encoder_attn.k_proj: 37.5
  encoder_attn.out_proj: 753.0
  encoder_attn.q_proj: 2.33203125
  encoder_attn.v_proj: 27.171875
  fc1: 29.703125
  fc2: 0.48193359375
  self_attn.k_proj: 2.5234375
  self_attn.out_proj: 0.45947265625
  self_attn.q_proj: 2.48828125
  self_attn.v_proj: 1.287109375
- encoder_attn.k_proj: 38.9375
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 3.58203125
  encoder_attn.v_proj: 26.8125
  fc1: 20.546875
  fc2: 0.52001953125
  self_attn.k_proj: 4.15234375
  self_attn.out_proj: 1.0771484375
  self_attn.q_proj: 3.43359375
  self_attn.v_proj: 1.7080078125
- encoder_attn.k_proj: 38.84375
  encoder_attn.out_proj: 659.0
  encoder_attn.q_proj: 2.791015625
  encoder_attn.v_proj: 26.109375
  fc1: 24.0
  fc2: 0.48779296875
  self_attn.k_proj: 2.650390625
  self_attn.out_proj: 1.7392578125
  self_attn.q_proj: 2.7890625
  self_attn.v_proj: 1.4677734375
- encoder_attn.k_proj: 35.34375
  encoder_attn.out_proj: 709.0
  encoder_attn.q_proj: 3.740234375
  encoder_attn.v_proj: 30.875
  fc1: 15.7109375
  fc2: 0.2237548828125
  self_attn.k_proj: 4.98828125
  self_attn.out_proj: 0.5283203125
  self_attn.q_proj: 3.9921875
  self_attn.v_proj: 1.974609375
- encoder_attn.k_proj: 40.25
  encoder_attn.out_proj: 845.0
  encoder_attn.q_proj: 2.93359375
  encoder_attn.v_proj: 28.03125
  fc1: 12.90625
  fc2: 0.56640625
  self_attn.k_proj: 2.513671875
  self_attn.out_proj: 0.630859375
  self_attn.q_proj: 2.361328125
  self_attn.v_proj: 1.2373046875
- encoder_attn.k_proj: 38.21875
  encoder_attn.out_proj: 684.5
  encoder_attn.q_proj: 2.474609375
  encoder_attn.v_proj: 26.8125
  fc1: 9.96875
  fc2: 0.5224609375
  self_attn.k_proj: 2.6328125
  self_attn.out_proj: 1.30859375
  self_attn.q_proj: 2.12890625
  self_attn.v_proj: 1.0029296875
- encoder_attn.k_proj: 40.125
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.890625
  encoder_attn.v_proj: 28.859375
  fc1: 13.1953125
  fc2: 0.6513671875
  self_attn.k_proj: 2.4765625
  self_attn.out_proj: 0.7587890625
  self_attn.q_proj: 2.611328125
  self_attn.v_proj: 1.3134765625
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 2.271484375
  encoder_attn.v_proj: 26.265625
  fc1: 12.5703125
  fc2: 0.486572265625
  self_attn.k_proj: 2.7734375
  self_attn.out_proj: 0.48681640625
  self_attn.q_proj: 2.501953125
  self_attn.v_proj: 1.2197265625
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: 845.0
  encoder_attn.q_proj: 1.7880859375
  encoder_attn.v_proj: 30.8125
  fc1: 15.453125
  fc2: 0.50341796875
  self_attn.k_proj: 2.509765625
  self_attn.out_proj: 0.6416015625
  self_attn.q_proj: 2.66796875
  self_attn.v_proj: 1.3916015625
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.76953125
  encoder_attn.v_proj: 27.828125
  fc1: 16.625
  fc2: 0.51123046875
  self_attn.k_proj: 2.6171875
  self_attn.out_proj: 1.3291015625
  self_attn.q_proj: 2.849609375
  self_attn.v_proj: 1.1357421875
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: 703.0
  encoder_attn.q_proj: 1.693359375
  encoder_attn.v_proj: 26.21875
  fc1: 18.734375
  fc2: 0.537109375
  self_attn.k_proj: 3.3125
  self_attn.out_proj: 0.595703125
  self_attn.q_proj: 2.91796875
  self_attn.v_proj: 1.1962890625
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 2.140625
  encoder_attn.v_proj: 28.9375
  fc1: 16.0
  fc2: 0.6748046875
  self_attn.k_proj: 3.583984375
  self_attn.out_proj: 0.900390625
  self_attn.q_proj: 3.33984375
  self_attn.v_proj: 1.7744140625
- encoder_attn.k_proj: .inf
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.419921875
  encoder_attn.v_proj: 32.0
  fc1: 11.6875
  fc2: 1.2421875
  self_attn.k_proj: 2.8671875
  self_attn.out_proj: 0.8857421875
  self_attn.q_proj: 3.580078125
  self_attn.v_proj: 0.96533203125
encoder:
- fc1: .inf
  fc2: 0.3837890625
  self_attn.k_proj: 4.2734375
  self_attn.out_proj: 0.4775390625
  self_attn.q_proj: .inf
  self_attn.v_proj: 3.0625
- fc1: 4.67578125
  fc2: 0.07220458984375
  self_attn.k_proj: 1.328125
  self_attn.out_proj: 0.16259765625
  self_attn.q_proj: 1.17578125
  self_attn.v_proj: 0.56982421875
- fc1: .inf
  fc2: 0.019195556640625
  self_attn.k_proj: 1.0068359375
  self_attn.out_proj: 0.167724609375
  self_attn.q_proj: 0.8935546875
  self_attn.v_proj: 0.53955078125
- fc1: .inf
  fc2: 0.0309906005859375
  self_attn.k_proj: 1.171875
  self_attn.out_proj: 0.1220703125
  self_attn.q_proj: 0.888671875
  self_attn.v_proj: 0.55615234375
- fc1: 4.96484375
  fc2: 0.024139404296875
  self_attn.k_proj: 1.1708984375
  self_attn.out_proj: 0.12164306640625
  self_attn.q_proj: 0.83984375
  self_attn.v_proj: 0.564453125
- fc1: .inf
  fc2: 0.0254058837890625
  self_attn.k_proj: 1.111328125
  self_attn.out_proj: 0.1475830078125
  self_attn.q_proj: 0.7431640625
  self_attn.v_proj: 0.59521484375
- fc1: .inf
  fc2: 0.028350830078125
  self_attn.k_proj: 1.5703125
  self_attn.out_proj: 0.137451171875
  self_attn.q_proj: 1.0556640625
  self_attn.v_proj: 0.7109375
- fc1: .inf
  fc2: 0.042449951171875
  self_attn.k_proj: 1.7216796875
  self_attn.out_proj: 0.146240234375
  self_attn.q_proj: 1.2099609375
  self_attn.v_proj: 0.74365234375
- fc1: .inf
  fc2: 0.060211181640625
  self_attn.k_proj: 1.7978515625
  self_attn.out_proj: 0.1619873046875
  self_attn.q_proj: 1.251953125
  self_attn.v_proj: 0.767578125
- fc1: .inf
  fc2: 0.046478271484375
  self_attn.k_proj: 1.6689453125
  self_attn.out_proj: 0.1373291015625
  self_attn.q_proj: 1.134765625
  self_attn.v_proj: 0.75390625
- fc1: .inf
  fc2: 0.05413818359375
  self_attn.k_proj: 1.3642578125
  self_attn.out_proj: 0.1778564453125
  self_attn.q_proj: 1.0009765625
  self_attn.v_proj: 0.76708984375
- fc1: .inf
  fc2: 0.052703857421875
  self_attn.k_proj: 1.6171875
  self_attn.out_proj: 0.1640625
  self_attn.q_proj: 1.0732421875
  self_attn.v_proj: 0.759765625
- fc1: .inf
  fc2: 0.0782470703125
  self_attn.k_proj: 1.626953125
  self_attn.out_proj: 0.16064453125
  self_attn.q_proj: 1.033203125
  self_attn.v_proj: 0.69189453125
- fc1: .inf
  fc2: 0.100341796875
  self_attn.k_proj: 1.4365234375
  self_attn.out_proj: 0.1749267578125
  self_attn.q_proj: 0.9638671875
  self_attn.v_proj: 0.64306640625
- fc1: .inf
  fc2: 0.12841796875
  self_attn.k_proj: 1.6474609375
  self_attn.out_proj: 0.125244140625
  self_attn.q_proj: 1.099609375
  self_attn.v_proj: 0.64013671875
- fc1: .inf
  fc2: 0.114501953125
  self_attn.k_proj: 1.5380859375
  self_attn.out_proj: 0.173828125
  self_attn.q_proj: 1.068359375
  self_attn.v_proj: 0.64111328125
- fc1: .inf
  fc2: 0.13818359375
  self_attn.k_proj: 1.4072265625
  self_attn.out_proj: 0.1778564453125
  self_attn.q_proj: 0.8857421875
  self_attn.v_proj: 0.67236328125
- fc1: .inf
  fc2: 0.1614990234375
  self_attn.k_proj: 1.7822265625
  self_attn.out_proj: 0.171630859375
  self_attn.q_proj: 1.2919921875
  self_attn.v_proj: 0.8095703125
- fc1: .inf
  fc2: 0.1962890625
  self_attn.k_proj: 1.8505859375
  self_attn.out_proj: 0.2064208984375
  self_attn.q_proj: 1.23046875
  self_attn.v_proj: 0.810546875
- fc1: .inf
  fc2: 0.23779296875
  self_attn.k_proj: 1.7490234375
  self_attn.out_proj: 0.197998046875
  self_attn.q_proj: 1.357421875
  self_attn.v_proj: 0.8076171875
- fc1: .inf
  fc2: 0.285400390625
  self_attn.k_proj: 1.990234375
  self_attn.out_proj: 0.294189453125
  self_attn.q_proj: 1.8369140625
  self_attn.v_proj: 0.9521484375
- fc1: .inf
  fc2: 0.275634765625
  self_attn.k_proj: 1.9990234375
  self_attn.out_proj: 0.2427978515625
  self_attn.q_proj: 1.7822265625
  self_attn.v_proj: 0.970703125
- fc1: .inf
  fc2: 0.28271484375
  self_attn.k_proj: 2.279296875
  self_attn.out_proj: 0.3115234375
  self_attn.q_proj: 2.244140625
  self_attn.v_proj: 1.0849609375
- fc1: .inf
  fc2: 0.29541015625
  self_attn.k_proj: 2.220703125
  self_attn.out_proj: 0.274169921875
  self_attn.q_proj: 2.21484375
  self_attn.v_proj: 1.125
- fc1: .inf
  fc2: 0.31494140625
  self_attn.k_proj: 2.6328125
  self_attn.out_proj: 0.30517578125
  self_attn.q_proj: 2.435546875
  self_attn.v_proj: 1.2451171875
- fc1: .inf
  fc2: 0.344482421875
  self_attn.k_proj: 2.4765625
  self_attn.out_proj: 0.296630859375
  self_attn.q_proj: 2.3515625
  self_attn.v_proj: 1.1884765625
- fc1: .inf
  fc2: 0.348876953125
  self_attn.k_proj: 2.666015625
  self_attn.out_proj: 0.27880859375
  self_attn.q_proj: 2.515625
  self_attn.v_proj: 1.3095703125
- fc1: .inf
  fc2: 0.3955078125
  self_attn.k_proj: 2.54296875
  self_attn.out_proj: 0.224609375
  self_attn.q_proj: 2.208984375
  self_attn.v_proj: 1.21875
- fc1: .inf
  fc2: 0.469970703125
  self_attn.k_proj: 2.69921875
  self_attn.out_proj: 0.36181640625
  self_attn.q_proj: 2.443359375
  self_attn.v_proj: 1.42578125
- fc1: .inf
  fc2: 0.51123046875
  self_attn.k_proj: 3.0703125
  self_attn.out_proj: 0.46044921875
  self_attn.q_proj: 2.9609375
  self_attn.v_proj: 1.7490234375
- fc1: .inf
  fc2: 0.58544921875
  self_attn.k_proj: 2.919921875
  self_attn.out_proj: 0.490478515625
  self_attn.q_proj: 2.8984375
  self_attn.v_proj: 1.8173828125
- fc1: .inf
  fc2: 0.6767578125
  self_attn.k_proj: 3.138671875
  self_attn.out_proj: 0.54833984375
  self_attn.q_proj: 3.203125
  self_attn.v_proj: 1.9794921875
