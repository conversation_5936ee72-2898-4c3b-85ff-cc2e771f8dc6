# export CUDA_VISIBLE_DEVICES=0

model=openai/whisper-large-v3
LOGFILE=/exp/tianteng.gu/projects/sparsegpt-main/exp/log-0.5

/home/<USER>/miniconda3/envs/sparse/bin/python /exp/tianteng.gu/projects/sparsegpt-master/whisper.py  \
    --model $model \
    --dataset libri \
    --nsamples 512 \
    --blocksize 256 \
    --encoder_sparsity 0.7 \
    --decoder_sparsity 0.7 \
    --eval false \
    --save /exp/tianteng.gu/projects/sparsegpt-master/exp/uniform_0.7_clean