decoder:
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.63
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.62
  self_attn.q_proj: 0.31
  self_attn.v_proj: 0.49
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.65
  fc2: 0.48
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.36
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.6
  fc2: 0.51
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.43
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.34
  fc1: 0.64
  fc2: 0.49
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.35
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.65
  fc2: 0.64
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.35
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.57
  fc2: 0.65
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.53
  fc2: 0.62
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.31
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.55
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.61
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.49
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.43
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.43
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.41
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.41
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.51
  fc2: 0.41
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.51
  fc2: 0.4
  self_attn.k_proj: 0.63
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.51
  fc2: 0.4
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.52
  fc2: 0.39
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.53
  fc2: 0.4
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.53
  fc2: 0.39
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.53
  fc2: 0.32
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.53
  fc2: 0.39
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.52
  fc2: 0.38
  self_attn.k_proj: 0.64
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.51
  fc2: 0.38
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.36
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.37
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.37
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.5
  fc2: 0.38
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.65
- encoder_attn.k_proj: 0.3
  encoder_attn.out_proj: 0.3
  encoder_attn.q_proj: 0.65
  encoder_attn.v_proj: 0.3
  fc1: 0.56
  fc2: 0.38
  self_attn.k_proj: 0.65
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.65
  self_attn.v_proj: 0.65
encoder:
- fc1: 0.5
  fc2: 0.5
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.5
  self_attn.q_proj: 0.5
  self_attn.v_proj: 0.5
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.56
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.6
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.61
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.62
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.58
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.57
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.61
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.59
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.64
  self_attn.v_proj: 0.65
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.54
  self_attn.v_proj: 0.59
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.47
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.58
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.46
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.56
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.46
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.55
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.53
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.45
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.55
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.46
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.57
- fc1: 0.5
  fc2: 0.65
  self_attn.k_proj: 0.47
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.51
  self_attn.v_proj: 0.57
- fc1: 0.5
  fc2: 0.61
  self_attn.k_proj: 0.46
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.49
  self_attn.v_proj: 0.58
- fc1: 0.5
  fc2: 0.64
  self_attn.k_proj: 0.45
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.48
  self_attn.v_proj: 0.54
- fc1: 0.5
  fc2: 0.58
  self_attn.k_proj: 0.49
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.54
  self_attn.v_proj: 0.58
- fc1: 0.5
  fc2: 0.52
  self_attn.k_proj: 0.43
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.45
  self_attn.v_proj: 0.52
- fc1: 0.5
  fc2: 0.47
  self_attn.k_proj: 0.43
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.45
  self_attn.v_proj: 0.52
- fc1: 0.5
  fc2: 0.42
  self_attn.k_proj: 0.43
  self_attn.out_proj: 0.65
  self_attn.q_proj: 0.44
  self_attn.v_proj: 0.52
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.37
  self_attn.out_proj: 0.59
  self_attn.q_proj: 0.36
  self_attn.v_proj: 0.45
- fc1: 0.5
  fc2: 0.33
  self_attn.k_proj: 0.36
  self_attn.out_proj: 0.61
  self_attn.q_proj: 0.35
  self_attn.v_proj: 0.45
- fc1: 0.5
  fc2: 0.33
  self_attn.k_proj: 0.32
  self_attn.out_proj: 0.54
  self_attn.q_proj: 0.31
  self_attn.v_proj: 0.41
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.33
  self_attn.out_proj: 0.57
  self_attn.q_proj: 0.32
  self_attn.v_proj: 0.41
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.52
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.38
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.57
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.37
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.54
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.35
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.63
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.36
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.49
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.33
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.42
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.3
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.42
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.3
- fc1: 0.5
  fc2: 0.3
  self_attn.k_proj: 0.3
  self_attn.out_proj: 0.36
  self_attn.q_proj: 0.3
  self_attn.v_proj: 0.3
