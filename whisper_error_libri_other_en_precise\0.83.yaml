decoder:
- encoder_attn.k_proj: 12.71875
  encoder_attn.out_proj: 326.0
  encoder_attn.q_proj: 4.6875
  encoder_attn.v_proj: 13.109375
  fc1: 4.12890625
  fc2: 3.833984375
  self_attn.k_proj: 23.609375
  self_attn.out_proj: 2.216796875
  self_attn.q_proj: 20.5625
  self_attn.v_proj: 7.4609375
- encoder_attn.k_proj: 13.3203125
  encoder_attn.out_proj: 126.8125
  encoder_attn.q_proj: 0.6396484375
  encoder_attn.v_proj: 13.625
  fc1: 4.1875
  fc2: 0.98193359375
  self_attn.k_proj: 1.5048828125
  self_attn.out_proj: 0.037353515625
  self_attn.q_proj: 1.2958984375
  self_attn.v_proj: 0.280517578125
- encoder_attn.k_proj: 8.03125
  encoder_attn.out_proj: 26.109375
  encoder_attn.q_proj: 0.81201171875
  encoder_attn.v_proj: 8.0
  fc1: 8.0
  fc2: 0.451416015625
  self_attn.k_proj: 0.392333984375
  self_attn.out_proj: 0.0362548828125
  self_attn.q_proj: 0.68115234375
  self_attn.v_proj: 0.1470947265625
- encoder_attn.k_proj: 6.3984375
  encoder_attn.out_proj: 39.34375
  encoder_attn.q_proj: 0.03826904296875
  encoder_attn.v_proj: 5.37890625
  fc1: 4.75
  fc2: 0.83251953125
  self_attn.k_proj: 0.1763916015625
  self_attn.out_proj: 0.0239715576171875
  self_attn.q_proj: 0.308837890625
  self_attn.v_proj: 0.1531982421875
- encoder_attn.k_proj: 12.9140625
  encoder_attn.out_proj: 110.9375
  encoder_attn.q_proj: 0.61669921875
  encoder_attn.v_proj: 12.1875
  fc1: 3.99609375
  fc2: 0.71337890625
  self_attn.k_proj: 0.266357421875
  self_attn.out_proj: 0.0289154052734375
  self_attn.q_proj: 0.64599609375
  self_attn.v_proj: 0.21142578125
- encoder_attn.k_proj: 10.53125
  encoder_attn.out_proj: 169.625
  encoder_attn.q_proj: 0.65087890625
  encoder_attn.v_proj: 10.0703125
  fc1: 8.0
  fc2: 0.1417236328125
  self_attn.k_proj: 0.5
  self_attn.out_proj: 0.2215576171875
  self_attn.q_proj: 0.76318359375
  self_attn.v_proj: 0.307373046875
- encoder_attn.k_proj: 18.015625
  encoder_attn.out_proj: 154.75
  encoder_attn.q_proj: 0.8369140625
  encoder_attn.v_proj: 16.28125
  fc1: 8.9296875
  fc2: 0.1617431640625
  self_attn.k_proj: 0.77392578125
  self_attn.out_proj: 0.22998046875
  self_attn.q_proj: 0.8759765625
  self_attn.v_proj: 0.36181640625
- encoder_attn.k_proj: 13.515625
  encoder_attn.out_proj: 211.625
  encoder_attn.q_proj: 1.3857421875
  encoder_attn.v_proj: 13.84375
  fc1: 8.5
  fc2: 0.1168212890625
  self_attn.k_proj: 0.994140625
  self_attn.out_proj: 0.3291015625
  self_attn.q_proj: 0.96337890625
  self_attn.v_proj: 0.37353515625
- encoder_attn.k_proj: 11.3046875
  encoder_attn.out_proj: 117.375
  encoder_attn.q_proj: 1.5185546875
  encoder_attn.v_proj: 9.0859375
  fc1: 17.171875
  fc2: 0.0872802734375
  self_attn.k_proj: 1.1923828125
  self_attn.out_proj: 0.267822265625
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.39013671875
- encoder_attn.k_proj: 11.5859375
  encoder_attn.out_proj: 314.75
  encoder_attn.q_proj: 1.830078125
  encoder_attn.v_proj: 12.6328125
  fc1: 15.6328125
  fc2: 0.1214599609375
  self_attn.k_proj: 1.083984375
  self_attn.out_proj: 0.2249755859375
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.361083984375
- encoder_attn.k_proj: 17.40625
  encoder_attn.out_proj: 161.625
  encoder_attn.q_proj: 1.2578125
  encoder_attn.v_proj: 12.71875
  fc1: 15.828125
  fc2: 0.0953369140625
  self_attn.k_proj: 1.95703125
  self_attn.out_proj: 0.31787109375
  self_attn.q_proj: 1.46484375
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 17.234375
  encoder_attn.out_proj: 351.25
  encoder_attn.q_proj: 0.5
  encoder_attn.v_proj: 15.703125
  fc1: 13.875
  fc2: 0.10980224609375
  self_attn.k_proj: 1.27734375
  self_attn.out_proj: 0.2154541015625
  self_attn.q_proj: 1.2607421875
  self_attn.v_proj: 0.4794921875
- encoder_attn.k_proj: 18.640625
  encoder_attn.out_proj: 130.5
  encoder_attn.q_proj: 0.89453125
  encoder_attn.v_proj: 18.203125
  fc1: 9.921875
  fc2: 0.16796875
  self_attn.k_proj: 1.83984375
  self_attn.out_proj: 0.5
  self_attn.q_proj: 1.6669921875
  self_attn.v_proj: 0.59716796875
- encoder_attn.k_proj: 20.78125
  encoder_attn.out_proj: 407.0
  encoder_attn.q_proj: 0.8984375
  encoder_attn.v_proj: 18.078125
  fc1: 7.66796875
  fc2: 0.376953125
  self_attn.k_proj: 1.5341796875
  self_attn.out_proj: 0.371826171875
  self_attn.q_proj: 1.3212890625
  self_attn.v_proj: 0.7001953125
- encoder_attn.k_proj: 19.921875
  encoder_attn.out_proj: 270.75
  encoder_attn.q_proj: 0.493408203125
  encoder_attn.v_proj: 19.296875
  fc1: 8.0
  fc2: 0.19189453125
  self_attn.k_proj: 1.4482421875
  self_attn.out_proj: 0.3603515625
  self_attn.q_proj: 1.435546875
  self_attn.v_proj: 0.62109375
- encoder_attn.k_proj: 21.765625
  encoder_attn.out_proj: 303.5
  encoder_attn.q_proj: 0.681640625
  encoder_attn.v_proj: 20.0625
  fc1: 7.6796875
  fc2: 0.285888671875
  self_attn.k_proj: 1.8251953125
  self_attn.out_proj: 0.47412109375
  self_attn.q_proj: 1.7822265625
  self_attn.v_proj: 0.78076171875
- encoder_attn.k_proj: 21.9375
  encoder_attn.out_proj: 675.5
  encoder_attn.q_proj: 0.6376953125
  encoder_attn.v_proj: 21.515625
  fc1: 8.0
  fc2: 0.224853515625
  self_attn.k_proj: 1.4951171875
  self_attn.out_proj: 0.335693359375
  self_attn.q_proj: 1.4794921875
  self_attn.v_proj: 0.73291015625
- encoder_attn.k_proj: 21.75
  encoder_attn.out_proj: 437.0
  encoder_attn.q_proj: 0.9384765625
  encoder_attn.v_proj: 20.875
  fc1: 7.34765625
  fc2: 0.2225341796875
  self_attn.k_proj: 1.2822265625
  self_attn.out_proj: 0.57275390625
  self_attn.q_proj: 1.2666015625
  self_attn.v_proj: 0.7470703125
- encoder_attn.k_proj: 21.6875
  encoder_attn.out_proj: 709.0
  encoder_attn.q_proj: 1.287109375
  encoder_attn.v_proj: 21.84375
  fc1: 7.4375
  fc2: 0.203857421875
  self_attn.k_proj: 1.55859375
  self_attn.out_proj: 0.365478515625
  self_attn.q_proj: 1.4453125
  self_attn.v_proj: 0.8232421875
- encoder_attn.k_proj: 23.40625
  encoder_attn.out_proj: 551.5
  encoder_attn.q_proj: 1.078125
  encoder_attn.v_proj: 23.703125
  fc1: 8.8984375
  fc2: 0.2275390625
  self_attn.k_proj: 1.24609375
  self_attn.out_proj: 0.2137451171875
  self_attn.q_proj: 1.0693359375
  self_attn.v_proj: 0.5478515625
- encoder_attn.k_proj: 24.0
  encoder_attn.out_proj: 754.5
  encoder_attn.q_proj: 1.3349609375
  encoder_attn.v_proj: 23.46875
  fc1: 7.02734375
  fc2: 0.23388671875
  self_attn.k_proj: 1.9462890625
  self_attn.out_proj: 0.4033203125
  self_attn.q_proj: 1.416015625
  self_attn.v_proj: 0.69775390625
- encoder_attn.k_proj: 23.6875
  encoder_attn.out_proj: 506.25
  encoder_attn.q_proj: 1.1201171875
  encoder_attn.v_proj: 22.625
  fc1: 7.765625
  fc2: 0.2283935546875
  self_attn.k_proj: 1.138671875
  self_attn.out_proj: 0.6494140625
  self_attn.q_proj: 1.19140625
  self_attn.v_proj: 0.6044921875
- encoder_attn.k_proj: 23.140625
  encoder_attn.out_proj: 529.5
  encoder_attn.q_proj: 1.658203125
  encoder_attn.v_proj: 26.734375
  fc1: 5.49609375
  fc2: 0.125
  self_attn.k_proj: 1.9814453125
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.427734375
  self_attn.v_proj: 0.73046875
- encoder_attn.k_proj: 25.390625
  encoder_attn.out_proj: 647.5
  encoder_attn.q_proj: 1.1435546875
  encoder_attn.v_proj: 24.125
  fc1: 4.84765625
  fc2: 0.2822265625
  self_attn.k_proj: 1.23046875
  self_attn.out_proj: 0.307861328125
  self_attn.q_proj: 1.162109375
  self_attn.v_proj: 0.55615234375
- encoder_attn.k_proj: 23.40625
  encoder_attn.out_proj: 521.0
  encoder_attn.q_proj: 1.029296875
  encoder_attn.v_proj: 23.40625
  fc1: 4.0
  fc2: 0.25
  self_attn.k_proj: 1.22265625
  self_attn.out_proj: 0.5
  self_attn.q_proj: 1.099609375
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 25.15625
  encoder_attn.out_proj: 738.5
  encoder_attn.q_proj: 0.9912109375
  encoder_attn.v_proj: 24.890625
  fc1: 5.234375
  fc2: 0.2802734375
  self_attn.k_proj: 1.3291015625
  self_attn.out_proj: 0.3154296875
  self_attn.q_proj: 1.1171875
  self_attn.v_proj: 0.65087890625
- encoder_attn.k_proj: 26.859375
  encoder_attn.out_proj: 819.0
  encoder_attn.q_proj: 0.978515625
  encoder_attn.v_proj: 22.75
  fc1: 5.0390625
  fc2: 0.2042236328125
  self_attn.k_proj: 1.37109375
  self_attn.out_proj: 0.272216796875
  self_attn.q_proj: 1.228515625
  self_attn.v_proj: 0.60009765625
- encoder_attn.k_proj: 26.09375
  encoder_attn.out_proj: 571.5
  encoder_attn.q_proj: 0.85498046875
  encoder_attn.v_proj: 26.359375
  fc1: 5.703125
  fc2: 0.24853515625
  self_attn.k_proj: 1.2841796875
  self_attn.out_proj: 0.324462890625
  self_attn.q_proj: 1.1982421875
  self_attn.v_proj: 0.6767578125
- encoder_attn.k_proj: 26.90625
  encoder_attn.out_proj: 654.0
  encoder_attn.q_proj: 0.76513671875
  encoder_attn.v_proj: 25.34375
  fc1: 6.50390625
  fc2: 0.25830078125
  self_attn.k_proj: 1.1796875
  self_attn.out_proj: 0.51416015625
  self_attn.q_proj: 1.111328125
  self_attn.v_proj: 0.5546875
- encoder_attn.k_proj: 26.21875
  encoder_attn.out_proj: 534.5
  encoder_attn.q_proj: 0.8466796875
  encoder_attn.v_proj: 23.71875
  fc1: 6.98046875
  fc2: 0.25830078125
  self_attn.k_proj: 1.3984375
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.3154296875
  self_attn.v_proj: 0.564453125
- encoder_attn.k_proj: 28.390625
  encoder_attn.out_proj: 732.5
  encoder_attn.q_proj: 0.8994140625
  encoder_attn.v_proj: 25.65625
  fc1: 5.95703125
  fc2: 0.3134765625
  self_attn.k_proj: 1.7001953125
  self_attn.out_proj: 0.3955078125
  self_attn.q_proj: 1.4951171875
  self_attn.v_proj: 0.86474609375
- encoder_attn.k_proj: 29.453125
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 0.61962890625
  encoder_attn.v_proj: 27.296875
  fc1: 4.9921875
  fc2: 0.422607421875
  self_attn.k_proj: 1.3056640625
  self_attn.out_proj: 0.311767578125
  self_attn.q_proj: 1.2421875
  self_attn.v_proj: 0.433349609375
encoder:
- fc1: 5.171875
  fc2: 0.1329345703125
  self_attn.k_proj: 5.12109375
  self_attn.out_proj: 0.19091796875
  self_attn.q_proj: 4.12109375
  self_attn.v_proj: 3.15234375
- fc1: 2.4140625
  fc2: 0.0248870849609375
  self_attn.k_proj: 0.71142578125
  self_attn.out_proj: 0.0860595703125
  self_attn.q_proj: 0.6025390625
  self_attn.v_proj: 0.329345703125
- fc1: 2.755859375
  fc2: 0.0093231201171875
  self_attn.k_proj: 0.513671875
  self_attn.out_proj: 0.09136962890625
  self_attn.q_proj: 0.46728515625
  self_attn.v_proj: 0.31494140625
- fc1: 3.087890625
  fc2: 0.0129241943359375
  self_attn.k_proj: 0.59521484375
  self_attn.out_proj: 0.07373046875
  self_attn.q_proj: 0.47607421875
  self_attn.v_proj: 0.341064453125
- fc1: 2.51953125
  fc2: 0.008636474609375
  self_attn.k_proj: 0.599609375
  self_attn.out_proj: 0.07080078125
  self_attn.q_proj: 0.469970703125
  self_attn.v_proj: 0.345458984375
- fc1: 2.953125
  fc2: 0.00995635986328125
  self_attn.k_proj: 0.56103515625
  self_attn.out_proj: 0.0909423828125
  self_attn.q_proj: 0.414794921875
  self_attn.v_proj: 0.37158203125
- fc1: 3.220703125
  fc2: 0.01038360595703125
  self_attn.k_proj: 0.826171875
  self_attn.out_proj: 0.08367919921875
  self_attn.q_proj: 0.61474609375
  self_attn.v_proj: 0.460205078125
- fc1: 3.697265625
  fc2: 0.01552581787109375
  self_attn.k_proj: 0.8994140625
  self_attn.out_proj: 0.09490966796875
  self_attn.q_proj: 0.6923828125
  self_attn.v_proj: 0.4794921875
- fc1: 3.470703125
  fc2: 0.0250701904296875
  self_attn.k_proj: 0.94189453125
  self_attn.out_proj: 0.1082763671875
  self_attn.q_proj: 0.7255859375
  self_attn.v_proj: 0.50146484375
- fc1: .inf
  fc2: 0.02056884765625
  self_attn.k_proj: 0.9169921875
  self_attn.out_proj: 0.08880615234375
  self_attn.q_proj: 0.677734375
  self_attn.v_proj: 0.5009765625
- fc1: .inf
  fc2: 0.0238800048828125
  self_attn.k_proj: 0.75048828125
  self_attn.out_proj: 0.112060546875
  self_attn.q_proj: 0.6025390625
  self_attn.v_proj: 0.5048828125
- fc1: 4.6875
  fc2: 0.0228118896484375
  self_attn.k_proj: 0.9013671875
  self_attn.out_proj: 0.1036376953125
  self_attn.q_proj: 0.646484375
  self_attn.v_proj: 0.5
- fc1: .inf
  fc2: 0.035369873046875
  self_attn.k_proj: 0.90771484375
  self_attn.out_proj: 0.10369873046875
  self_attn.q_proj: 0.638671875
  self_attn.v_proj: 0.463134765625
- fc1: 3.69921875
  fc2: 0.05059814453125
  self_attn.k_proj: 0.7900390625
  self_attn.out_proj: 0.1138916015625
  self_attn.q_proj: 0.58544921875
  self_attn.v_proj: 0.4365234375
- fc1: .inf
  fc2: 0.064208984375
  self_attn.k_proj: 0.91064453125
  self_attn.out_proj: 0.0809326171875
  self_attn.q_proj: 0.66943359375
  self_attn.v_proj: 0.434814453125
- fc1: .inf
  fc2: 0.06072998046875
  self_attn.k_proj: 0.82958984375
  self_attn.out_proj: 0.0970458984375
  self_attn.q_proj: 0.63525390625
  self_attn.v_proj: 0.43505859375
- fc1: .inf
  fc2: 0.07220458984375
  self_attn.k_proj: 0.75390625
  self_attn.out_proj: 0.11541748046875
  self_attn.q_proj: 0.53857421875
  self_attn.v_proj: 0.435302734375
- fc1: .inf
  fc2: 0.08819580078125
  self_attn.k_proj: 0.99560546875
  self_attn.out_proj: 0.1153564453125
  self_attn.q_proj: 0.78564453125
  self_attn.v_proj: 0.54443359375
- fc1: .inf
  fc2: 0.114990234375
  self_attn.k_proj: 1.009765625
  self_attn.out_proj: 0.13232421875
  self_attn.q_proj: 0.74560546875
  self_attn.v_proj: 0.54052734375
- fc1: .inf
  fc2: 0.13818359375
  self_attn.k_proj: 0.97998046875
  self_attn.out_proj: 0.1300048828125
  self_attn.q_proj: 0.81787109375
  self_attn.v_proj: 0.544921875
- fc1: .inf
  fc2: 0.1904296875
  self_attn.k_proj: 1.1494140625
  self_attn.out_proj: 0.1876220703125
  self_attn.q_proj: 1.115234375
  self_attn.v_proj: 0.65576171875
- fc1: .inf
  fc2: 0.1728515625
  self_attn.k_proj: 1.1787109375
  self_attn.out_proj: 0.159423828125
  self_attn.q_proj: 1.111328125
  self_attn.v_proj: 0.66845703125
- fc1: .inf
  fc2: 0.1800537109375
  self_attn.k_proj: 1.373046875
  self_attn.out_proj: 0.2139892578125
  self_attn.q_proj: 1.390625
  self_attn.v_proj: 0.75927734375
- fc1: .inf
  fc2: 0.1942138671875
  self_attn.k_proj: 1.3388671875
  self_attn.out_proj: 0.181640625
  self_attn.q_proj: 1.359375
  self_attn.v_proj: 0.77685546875
- fc1: .inf
  fc2: 0.20751953125
  self_attn.k_proj: 1.603515625
  self_attn.out_proj: 0.218994140625
  self_attn.q_proj: 1.5341796875
  self_attn.v_proj: 0.87451171875
- fc1: .inf
  fc2: 0.23486328125
  self_attn.k_proj: 1.544921875
  self_attn.out_proj: 0.1961669921875
  self_attn.q_proj: 1.4970703125
  self_attn.v_proj: 0.8447265625
- fc1: .inf
  fc2: 0.2432861328125
  self_attn.k_proj: 1.6826171875
  self_attn.out_proj: 0.1983642578125
  self_attn.q_proj: 1.625
  self_attn.v_proj: 0.93017578125
- fc1: .inf
  fc2: 0.272705078125
  self_attn.k_proj: 1.60546875
  self_attn.out_proj: 0.1485595703125
  self_attn.q_proj: 1.427734375
  self_attn.v_proj: 0.8779296875
- fc1: .inf
  fc2: 0.310546875
  self_attn.k_proj: 1.74609375
  self_attn.out_proj: 0.2467041015625
  self_attn.q_proj: 1.6142578125
  self_attn.v_proj: 1.025390625
- fc1: .inf
  fc2: 0.341796875
  self_attn.k_proj: 1.984375
  self_attn.out_proj: 0.328369140625
  self_attn.q_proj: 1.953125
  self_attn.v_proj: 1.2333984375
- fc1: .inf
  fc2: 0.397705078125
  self_attn.k_proj: 1.9267578125
  self_attn.out_proj: 0.34033203125
  self_attn.q_proj: 1.9384765625
  self_attn.v_proj: 1.2802734375
- fc1: .inf
  fc2: 0.438720703125
  self_attn.k_proj: 2.03125
  self_attn.out_proj: 0.390869140625
  self_attn.q_proj: 2.11328125
  self_attn.v_proj: 1.359375
