import time

import torch
import torch.nn as nn

from modelutils import *
from datautils_whisper import *
from sparsegpt import *
import yaml
import os

from tqdm import tqdm

def get_whisper(model_id):
    import torch
    def skip(*args, **kwargs):
        pass
    torch.nn.init.kaiming_uniform_ = skip
    torch.nn.init.uniform_ = skip
    torch.nn.init.normal_ = skip
    from transformers import AutoModelForSpeechSeq2Seq
    torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    # torch_dtype = torch.float32
    model = AutoModelForSpeechSeq2Seq.from_pretrained(
        model_id, torch_dtype=torch_dtype
    )
    return model

@torch.no_grad()
def whisper_sequential(model, dev):

    # with open(args.sparsity_dict_path, 'r') as file:
    #     sparsity_dict = yaml.safe_load(file)

    ######### Encoder ##################
    print('Pruning encoder')
    layers = model.model.encoder.layers

    for i in range(len(layers)):
        layer = layers[i].to(dev)
        full = find_layers(layer)

        if args.true_sequential:
            sequential = [
                ['self_attn.k_proj', 'self_attn.v_proj', 'self_attn.q_proj'],
                ['self_attn.out_proj'],
                ['fc1'],
                ['fc2']
            ]
        else:
            sequential = [list(full.keys())]
       
        for names in sequential:
            subset = {n: full[n] for n in names}

            gpts = {}
            for name in subset:
                gpts[name] = SparseGPT(subset[name])

            for name in subset:
                # print(i, name)

                # sparsity = sparsity_dict["encoder"][i][name]
                sparsity = args.sparsity

                gpts[name].magnitude_prune(
                    sparsity,
                    prunen=args.prunen,
                    prunem=args.prunem,
                )
                gpts[name].free()

        layers[i] = layer.cpu()
        del layer
        del gpts 
        torch.cuda.empty_cache()

    torch.cuda.empty_cache()
    return


@torch.no_grad()
def whisper_eval(model, device):

    model = model.to(device)

    from datasets import load_dataset
    from transformers import WhisperForConditionalGeneration, WhisperProcessor
    import torch
    from evaluate import load

    selected_ids = [i for i in range(0, 2620, 1)]
    librispeech_test_clean = load_dataset("/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr", "clean", split="test", cache_dir="/exp/tianteng.gu/huggingface/cache").select(selected_ids)

    processor = WhisperProcessor.from_pretrained("openai/whisper-large-v3")

    def map_to_pred(batch):
        audio = batch["audio"]
        input_features = processor(audio["array"], sampling_rate=audio["sampling_rate"], return_tensors="pt").input_features
        batch["reference"] = processor.tokenizer._normalize(batch['text'])

        with torch.no_grad():
            predicted_ids = model.generate(input_features.to(device))[0]
        transcription = processor.decode(predicted_ids)
        batch["prediction"] = processor.tokenizer._normalize(transcription)
        return batch

    result = librispeech_test_clean.map(map_to_pred)
    wer = load("wer")
    print(100 * wer.compute(references=result["reference"], predictions=result["prediction"]))

if __name__ == '__main__':
    import argparse
    from datautils_whisper import *

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--model", 
        type=str, 
        help="whisper model to load"
    )
    parser.add_argument(
        "--dataset",
        type=str,
        help="Where to extract calibration data from.",
    )
    parser.add_argument(
        "--seed", type=int, default=0, help="Seed for sampling the calibration data."
    )
    parser.add_argument(
        "--nsamples", type=int, default=128, help="Number of calibration data samples."
    )
    parser.add_argument(
        "--percdamp",
        type=float,
        default=0.01,
        help="Percent of the average Hessian diagonal to use for dampening.",
    )
    parser.add_argument("--sparsity_dict_path", type=str, default="", help="")
    parser.add_argument("--sparsity", type=float, default="", help="")
    parser.add_argument("--prunen", type=int, default=0, help="N for N:M pruning.")
    parser.add_argument("--prunem", type=int, default=0, help="M for N:M pruning.")
    parser.add_argument(
        "--blocksize",
        type=int,
        default=128,
        help="Blocksize to use for adaptive mask selection.",
    )
    parser.add_argument(
        "--gmp", action="store_true", help="Whether to run the GMP baseline."
    )
    parser.add_argument(
        "--wbits", type=int, default=16, help="Whether to quantize as well."
    )
    parser.add_argument(
        "--minlayer", type=int, default=-1, help="Prune all layers with id >= this."
    )
    parser.add_argument(
        "--maxlayer", type=int, default=1000, help="Prune all layers with id < this."
    )
    parser.add_argument(
        "--prune_only",
        type=str,
        default="",
        help="Prune only layers that contain this text.",
    )
    parser.add_argument("--invert", action="store_true", help="Invert subset.")
    parser.add_argument(
        "--true-sequential",
        action="store_true",
        help="Whether to run in true sequential model.",
    )
    parser.add_argument(
        "--log_wandb", action="store_true", help="Whether to log to wandb."
    )
    parser.add_argument(
        '--eval', type=str,default="false",
        help='whether to evaluate.'
    )
    parser.add_argument(
        "--save", 
        default="",
        help="Path to saved model."
    )
    parser.add_argument(
        "--language", 
        default=""
    )
    parser.add_argument(
        "--task", 
        default=""
    )
    args = parser.parse_args()

    model = get_whisper(args.model)
    model.eval()

    DEV = torch.device("cuda:0")

    tick = time.time()
    whisper_sequential(model, DEV)
    # for n, p in model.named_parameters():
    #     print(n, torch.mean((p == 0).float()))
    #     if 'down_proj' in n:
    #         break
    print(time.time() - tick)

    if args.eval == "true":
        whisper_eval(model, DEV)

    if args.save != "":
        if not os.path.exists(args.save):
            os.makedirs(args.save)
        model.save_pretrained(args.save)
        from transformers import AutoProcessor
        processor = AutoProcessor.from_pretrained(args.model)
        processor.save_pretrained(args.save)

    
