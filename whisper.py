# 导入时间模块，用于计算程序运行时间
import time

# 导入PyTorch深度学习框架的核心模块
import torch
# 导入PyTorch的神经网络模块，包含各种网络层和激活函数
import torch.nn as nn

# 导入自定义的模型工具函数（*表示导入所有公开函数）
from modelutils import *
# 导入Whisper数据处理工具函数
from datautils_whisper import *
# 导入稀疏GPT相关功能
from sparsegpt import *
# 导入YAML配置文件解析库
import yaml
# 导入操作系统接口模块，用于文件和目录操作
import os
# 导入进度条显示库，用于显示循环进度
from tqdm import tqdm

# 定义函数：获取Whisper模型
# 参数model：模型名称（在此函数中未使用）
def get_whisper(model):
    # 在函数内部重新导入torch（虽然上面已导入，这里可能是为了确保可用性）
    import torch

    # 定义一个空函数，用于跳过某些初始化操作
    # *args接收任意数量的位置参数，**kwargs接收任意数量的关键字参数
    def skip(*args, **kwargs):
        pass  # pass语句表示什么都不做，用作占位符

    # 将PyTorch的权重初始化函数替换为空函数，跳过权重初始化
    # 这样做可以加快模型加载速度
    torch.nn.init.kaiming_uniform_ = skip  # 跳过Kaiming均匀分布初始化
    torch.nn.init.uniform_ = skip          # 跳过均匀分布初始化
    torch.nn.init.normal_ = skip           # 跳过正态分布初始化

    # 从transformers库导入语音到文本序列模型类
    from transformers import AutoModelForSpeechSeq2Seq

    # 根据CUDA是否可用选择数据类型：GPU用float16（半精度），CPU用float32（单精度）
    # 三元运算符：条件 if 条件 else 备选值
    torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    # torch_dtype = torch.float32  # 注释掉的代码，强制使用float32

    # 指定要加载的Whisper模型ID（OpenAI的大型v3版本）
    model_id = "openai/whisper-large-v3"

    # 从预训练模型加载Whisper模型
    # from_pretrained是类方法，用于加载预训练模型
    model = AutoModelForSpeechSeq2Seq.from_pretrained(
        model_id, torch_dtype=torch_dtype  # 指定模型ID和数据类型
    )

    # 返回加载的模型对象
    return model

@torch.no_grad()
def whisper_sequential(model, dataloader, dev):

    # 将数据加载器中的所有数据转换为模型的数据类型
    # range(len(dataloader))生成从0到数据长度-1的整数序列
    for i in range(len(dataloader)):
        # .to(model.dtype)将数据转换为模型的数据类型（如float16或float32）
        dataloader[i] = dataloader[i].to(model.dtype)

    # 打印提示信息：开始生成编码器隐藏状态
    print('Generating encoder hidden states ...')
    # 初始化空列表，用于存储编码器的隐藏状态
    encoder_hidden_states = []
    # 将模型的编码器部分移动到指定设备（通常是GPU）
    model.model.encoder = model.model.encoder.to(dev)  # 将编码器移到GPU
    # 设置批处理大小为16，控制每次处理的样本数量
    batch_size = 16

    # 使用tqdm显示进度条，以步长16遍历数据加载器
    # range(0, len(dataloader), 16)：从0开始，到数据长度，步长为16
    for i in tqdm(range(0, len(dataloader), 16)):
        # 计算当前批次的起始索引
        start = i
        # 计算当前批次的结束索引，使用min确保不超出数据范围
        end = min(len(dataloader), start+batch_size)
        # 从数据加载器中提取当前批次的数据
        batch = dataloader[start:end]
        # 使用torch.concatenate在维度0上拼接批次数据
        batch = torch.concatenate(batch, dim=0) 
        # 通过编码器处理批次数据，获取隐藏状态并移回CPU
        e_h_s = model.model.encoder(batch.to(dev))[0].cpu() 
        # 将批次输出拆分为单个样本并添加批次维度，然后添加到列表中
        # 列表推导式：[表达式 for 变量 in 可迭代对象]
        encoder_hidden_states += [x.unsqueeze(0) for x in e_h_s] 
    # 将编码器移回CPU以释放GPU内存
    model.model.encoder = model.model.encoder.cpu() 

    # 生成预测ID序列（model生成文本）
    print('Generating predicted ids ...')
    # 初始化空列表，用于存储预测的token ID
    predicted_ids = []
    # 将完整模型移动到指定设备（GPU）
    model = model.to(dev)  
    # 重新设置批处理大小（虽然值相同，但为了代码清晰性）
    batch_size = 16 

    # 使用tqdm显示进度条，每批处理16个样本
    for i in tqdm(range(0, len(dataloader), 16)):
        # 计算当前批次的起止索引
        start = i
        end = min(len(dataloader), start+batch_size)

        # 获取并合并当前批次数据
        batch = dataloader[start:end]
        batch = torch.concatenate(batch, dim=0) 

        # 使用模型的generate方法生成转录文本的token ID
        p_i = model.generate(
            batch.to(dev),  # 将数据移至GPU
            language="en",   # 指定英语语言模型
            task="transcribe"  # 设置转录任务模式
        ).cpu()  # 将结果移回CPU内存

        # 清理特殊结束标记（ID 50247）
        # list(p_i)将张量转换为Python列表进行遍历
        for x in list(p_i):
            # 获取序列的最后一个索引位置
            last_index = len(x) - 1
            # 使用while循环逆向遍历，删除连续的结束标记
            # 条件：索引有效且当前token是结束标记(50247)
            while last_index >= 0 and x[last_index] == 50247:
                last_index -= 1  # 向前移动索引

            # 保留有效token（从开始到最后一个非结束标记）
            # 切片操作：x[:last_index + 1]
            cleaned_x = x[:last_index + 1]
            # 使用unsqueeze(0)在第0维添加批次维度
            cleaned_x = cleaned_x.unsqueeze(0)  # 添加批次维度
            # 将清理后的序列添加到预测ID列表中
            predicted_ids.append(cleaned_x)

    # 将模型移回CPU释放显存
    model = model.cpu()

    # 保存模型原始的缓存配置
    use_cache = model.config.use_cache
    # 禁用缓存以节省内存（在剪枝过程中不需要缓存）
    model.config.use_cache = False

    ######### Encoder ##################
    print('Pruning encoder')
    # 获取编码器的所有层，layers是一个包含多个Transformer层的列表
    layers = model.model.encoder.layers

    # 将编码器的关键组件移动到GPU设备
    # conv1和conv2是编码器中的卷积层，用于处理音频特征
    model.model.encoder.conv1 = model.model.encoder.conv1.to(dev)
    model.model.encoder.conv2 = model.model.encoder.conv2.to(dev)
    # embed_positions是位置编码层，为序列添加位置信息
    model.model.encoder.embed_positions = model.model.encoder.embed_positions.to(dev)
    # 将第一层移动到GPU（用于捕获输入数据）
    layers[0] = layers[0].to(dev)

    # 获取模型参数的数据类型（float16或float32）
    # next(iter(model.parameters()))获取模型的第一个参数
    dtype = next(iter(model.parameters())).dtype
    # 初始化输入列表，用于存储每个样本的输入数据
    # 列表推导式：为每个数据样本创建一个占位符
    inps = [0 for i in range(len(dataloader))]
    # 创建缓存字典，用于存储索引和注意力掩码
    cache = {'i': 0, 'attention_mask': None}

    # 定义捕获器类，继承自PyTorch的神经网络模块
    # 这个类用于拦截和保存第一层的输入数据
    class Catcher_encoder(torch.nn.Module):
        # 初始化方法，接收要包装的模块
        def __init__(self, module):
            # 调用父类的初始化方法
            super().__init__()
            # 保存原始模块的引用
            self.module = module

        # 前向传播方法，定义数据如何通过这个层
        # **kwargs接收任意数量的关键字参数
        def forward(self, inp, attention_mask, layer_head_mask, **kwargs):
            # 将输入数据保存到inps列表中的当前索引位置
            inps[cache['i']] = inp
            # 增加索引计数器
            cache['i'] += 1
            # 保存注意力掩码
            cache['attention_mask'] = attention_mask
            # 抛出ValueError异常来中断正常的前向传播
            # 这样可以只捕获输入而不执行实际计算
            raise ValueError

    # 用捕获器包装第一层
    layers[0] = Catcher_encoder(layers[0])

    # 遍历数据加载器中的每个批次
    for batch in dataloader:
        try:
            # 尝试运行模型，将批次数据移动到设备
            model(batch.to(dev))
        except ValueError:
            # 捕获ValueError异常（由Catcher_encoder抛出）
            # pass表示忽略异常，继续执行
            pass

    # 恢复第一层的原始模块（移除捕获器包装）
    layers[0] = layers[0].module

    # 将所有组件移回CPU以释放GPU内存
    layers[0] = layers[0].cpu()
    model.model.encoder.conv1 = model.model.encoder.conv1.cpu()
    model.model.encoder.conv2 = model.model.encoder.conv2.cpu()
    model.model.encoder.embed_positions = model.model.encoder.embed_positions.cpu()
    # 清空CUDA缓存以释放GPU内存
    torch.cuda.empty_cache()

    # 初始化输出列表，用于存储每层的输出
    outs = [0 for i in range(len(dataloader))]
    # 从缓存中获取注意力掩码
    attention_mask = cache['attention_mask']

    # 打印准备完成的提示信息
    print('Ready.')

    # 初始化量化器字典（虽然在这里没有使用量化）
    quantizers = {}

    # 遍历编码器的每一层进行剪枝
    # range(len(layers))生成从0到层数-1的索引
    for i in range(len(layers)):
        # 将当前层移动到GPU设备
        layer = layers[i].to(dev)
        # 使用find_layers函数找到当前层中所有的线性层
        # 返回一个字典，键是层名，值是层对象
        full = find_layers(layer)

        # 根据args.true_sequential参数决定剪枝策略
        if args.true_sequential:
            # 真正的顺序剪枝：将层分组，每组包含相关的层
            sequential = [
                # 自注意力机制的查询、键、值投影层
                ['self_attn.k_proj', 'self_attn.v_proj', 'self_attn.q_proj'],
                # 自注意力的输出投影层
                ['self_attn.out_proj'],
                # 前馈网络的第一个全连接层
                ['fc1'],
                # 前馈网络的第二个全连接层
                ['fc2']
            ]
        else:
            # 非顺序剪枝：将所有层放在一个组中同时处理
            # list(full.keys())获取所有层名的列表
            sequential = [list(full.keys())]

        # 遍历每个层组进行剪枝
        for names in sequential:
            # 创建当前组的层子集字典
            # 字典推导式：{键: 值 for 变量 in 可迭代对象}
            subset = {n: full[n] for n in names}

            # 为当前组的每个层创建SparseGPT对象
            gpts = {}
            for name in subset:
                # SparseGPT是执行稀疏剪枝的核心类
                gpts[name] = SparseGPT(subset[name])

            # 定义添加批次数据的函数（闭包）
            def add_batch(name):
                # 内部函数，用作前向钩子
                # _表示未使用的参数，inp是输入，out是输出
                def tmp(_, inp, out):
                    # 将输入和输出数据添加到对应的SparseGPT对象中
                    # .data获取张量的数据部分（不包含梯度信息）
                    gpts[name].add_batch(inp[0].data, out.data)
                return tmp

            # 注册前向钩子以收集数据
            handles = []
            for name in subset:
                # register_forward_hook注册一个在前向传播时调用的钩子函数
                handles.append(subset[name].register_forward_hook(add_batch(name)))

            # 运行前向传播以收集统计信息
            # args.nsamples是校准数据的样本数量
            for j in range(args.nsamples):
                # 通过当前层处理输入数据，获取输出
                outs[j] = layer(inps[j], attention_mask=attention_mask, layer_head_mask=None)[0]

            # 移除所有注册的钩子
            for h in handles:
                h.remove()

            # 对当前组的每个层执行剪枝
            for name in subset:
                # 打印当前处理的层信息
                print(i, name)
                # 构造层的完整名称
                layername = '.'+str(i)+'.'+name

                # 根据层的位置设置稀疏度
                if i == 0:
                    # 第一层使用固定的稀疏度0.4（40%的权重被剪枝）
                    sparsity = 0.4
                else:
                    # 其他层使用命令行参数指定的编码器稀疏度
                    sparsity = args.encoder_sparsity

                # 执行快速剪枝算法
                gpts[name].fasterprune(
                    sparsity,                    # 稀疏度（要剪枝的权重比例）
                    prunen=args.prunen,          # N:M剪枝中的N值
                    prunem=args.prunem,          # N:M剪枝中的M值
                    percdamp=args.percdamp,      # 阻尼参数，用于数值稳定性
                    blocksize=args.blocksize,    # 块大小，用于分块处理
                )
                # 释放SparseGPT对象占用的内存
                gpts[name].free()

        # 剪枝完成后，重新运行前向传播获取新的输出
        for j in range(args.nsamples):
            outs[j] = layer(inps[j], attention_mask=attention_mask, layer_head_mask=None)[0]

        # 将当前层移回CPU并清理内存
        layers[i] = layer.cpu()
        del layer      # 删除层对象
        del gpts       # 删除SparseGPT对象
        torch.cuda.empty_cache()  # 清空CUDA缓存

        # 交换输入和输出列表，为下一层做准备
        # 当前层的输出成为下一层的输入
        inps, outs = outs, inps

    ######### Decoder ##################
    # 解码器剪枝部分开始
    print('Pruning decoder')  # 打印提示信息：开始剪枝解码器

    # 初始化注意力掩码列表，用于存储每个样本的注意力掩码
    attention_masks = [0 for i in range(len(dataloader))]
    # 获取模型参数的数据类型
    dtype = next(iter(model.parameters())).dtype
    # 创建缓存字典，只需要索引计数器（解码器不需要存储注意力掩码）
    cache = {'i': 0}

    # 获取解码器的所有层
    layers = model.model.decoder.layers

    # 将解码器的关键组件移动到GPU设备
    # embed_tokens是词嵌入层，将token ID转换为向量表示
    model.model.decoder.embed_tokens = model.model.decoder.embed_tokens.to(dev)
    # embed_positions是位置编码层，为序列添加位置信息
    model.model.decoder.embed_positions = model.model.decoder.embed_positions.to(dev)
    # 将第一层移动到GPU（用于捕获输入数据）
    layers[0] = layers[0].to(dev)

    # 初始化解码器的输入和输出列表
    decoder_inputs = [0 for i in range(len(dataloader))]
    decoder_outputs = [0 for i in range(len(dataloader))]

    # 定义解码器捕获器类，用于拦截和保存解码器第一层的输入
    class Catcher_decoder(torch.nn.Module):
        # 初始化方法
        def __init__(self, module):
            super().__init__()  # 调用父类初始化
            self.module = module  # 保存原始模块引用

        # 前向传播方法
        # hiddenstates是隐藏状态，**kwargs接收其他关键字参数
        def forward(self, hiddenstates, **kwargs):
            # 保存当前样本的隐藏状态（解码器输入）
            decoder_inputs[cache['i']] = hiddenstates
            # 从kwargs中提取并保存注意力掩码
            attention_masks[cache['i']] = kwargs['attention_mask']
            # 增加索引计数器
            cache['i'] += 1
            # 抛出异常中断前向传播，只捕获输入不执行计算
            raise ValueError

    # 用捕获器包装解码器的第一层
    layers[0] = Catcher_decoder(layers[0])

    # 遍历预测的ID序列，捕获解码器输入
    for batch in predicted_ids:
        try:
            # 尝试运行解码器，将批次数据移动到设备
            model.model.decoder(batch.to(dev))
        except ValueError:
            # 捕获ValueError异常（由Catcher_decoder抛出）
            pass  # 忽略异常，继续下一个批次

    # 恢复第一层的原始模块（移除捕获器包装）
    layers[0] = layers[0].module

    # 将所有组件移回CPU以释放GPU内存
    layers[0] = layers[0].cpu()
    model.model.decoder.embed_tokens = model.model.decoder.embed_tokens.cpu()
    model.model.decoder.embed_positions = model.model.decoder.embed_positions.cpu()
    # 清空CUDA缓存
    torch.cuda.empty_cache()

    # 打印准备完成的提示信息
    print('Ready.')
    # 遍历解码器的每一层进行剪枝
    for i in range(len(layers)):
        # 将当前层移动到GPU设备
        layer = layers[i].to(dev)
        # 找到当前层中所有的线性层
        full = find_layers(layer)

        # 根据args.true_sequential参数决定解码器的剪枝策略
        if args.true_sequential:
            # 真正的顺序剪枝：解码器比编码器多了编码器-解码器注意力层
            sequential = [
                # 自注意力机制的查询、键、值投影层
                ['self_attn.k_proj', 'self_attn.v_proj', 'self_attn.q_proj'],
                # 自注意力的输出投影层
                ['self_attn.out_proj'],
                # 编码器-解码器注意力的查询、键、值投影层
                ['encoder_attn.k_proj', 'encoder_attn.v_proj', 'encoder_attn.q_proj'],
                # 编码器-解码器注意力的输出投影层
                ['encoder_attn.out_proj'],
                # 前馈网络的第一个全连接层
                ['fc1'],
                # 前馈网络的第二个全连接层
                ['fc2']
            ]
        else:
            # 非顺序剪枝：将所有层放在一个组中同时处理
            sequential = [list(full.keys())]

        # 遍历每个层组进行剪枝
        for names in sequential:
            # 创建当前组的层子集
            subset = {n: full[n] for n in names}

            # 为当前组的每个层创建SparseGPT对象
            gpts = {}
            for name in subset:
                gpts[name] = SparseGPT(subset[name])

            # 定义添加批次数据的函数（与编码器相同的模式）
            def add_batch(name):
                def tmp(_, inp, out):
                    # 将输入和输出数据添加到SparseGPT对象中
                    gpts[name].add_batch(inp[0].data, out.data)
                return tmp

            # 注册前向钩子以收集数据
            handles = []
            for name in subset:
                handles.append(subset[name].register_forward_hook(add_batch(name)))

            # 运行前向传播以收集统计信息
            # 解码器需要额外的编码器隐藏状态作为输入
            for j in range(args.nsamples):
                # 解码器的前向传播需要三个输入：
                # 1. decoder_inputs[j]: 解码器的输入（目标序列）
                # 2. attention_mask: 注意力掩码
                # 3. encoder_hidden_states: 编码器的隐藏状态（用于编码器-解码器注意力）
                decoder_outputs[j] = layer(
                    decoder_inputs[j],
                    attention_mask=attention_masks[j],
                    encoder_hidden_states=encoder_hidden_states[j].to(dev)
                )[0]

            # 移除所有注册的钩子
            for h in handles:
                h.remove()

            # 对当前组的每个层执行剪枝
            for name in subset:
                print(i, name)  # 打印当前处理的层信息
                layername = '.'+str(i)+'.'+name  # 构造层的完整名称
                # 解码器使用统一的稀疏度设置
                sparsity = args.decoder_sparsity

                # 执行快速剪枝算法
                gpts[name].fasterprune(
                    sparsity,                    # 稀疏度
                    prunen=args.prunen,          # N:M剪枝中的N值
                    prunem=args.prunem,          # N:M剪枝中的M值
                    percdamp=args.percdamp,      # 阻尼参数
                    blocksize=args.blocksize,    # 块大小
                )
                # 释放SparseGPT对象占用的内存
                gpts[name].free()

        # 剪枝完成后，重新运行前向传播获取新的输出
        for j in range(args.nsamples):
            decoder_outputs[j] = layer(
                decoder_inputs[j],
                attention_mask=attention_masks[j],
                encoder_hidden_states=encoder_hidden_states[j].to(dev)
            )[0]

        # 将当前层移回CPU并清理内存
        layers[i] = layer.cpu()
        del layer      # 删除层对象
        del gpts       # 删除SparseGPT对象
        torch.cuda.empty_cache()  # 清空CUDA缓存

        # 交换输入和输出列表，为下一层做准备
        decoder_inputs, decoder_outputs = decoder_outputs, decoder_inputs

    # 恢复模型的原始缓存配置
    model.config.use_cache = use_cache

    # 函数结束，返回None（Python函数默认返回None）
    return


# 装饰器：禁用梯度计算，用于评估阶段
@torch.no_grad()
# 定义函数：评估Whisper模型的性能
# 参数：model-要评估的模型，device-运行设备
def whisper_eval(model, device):

    # 将模型移动到指定设备（通常是GPU）
    model = model.to(device)

    # 导入必要的库
    from datasets import load_dataset  # 用于加载数据集
    from transformers import WhisperForConditionalGeneration, WhisperProcessor  # Whisper相关类
    import torch  # PyTorch框架
    from evaluate import load  # 用于加载评估指标

    # 创建选择的样本ID列表，从0到2619，步长为1
    # 这里选择了LibriSpeech测试集的前2620个样本
    selected_ids = [i for i in range(0, 2620, 1)]

    # 加载LibriSpeech数据集的clean测试集
    # 这是一个常用的语音识别评估数据集
    librispeech_test_clean = load_dataset(
        "/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr",  # 数据集路径
        "clean",  # 数据集配置（clean表示清晰音频）
        split="test",  # 使用测试集
        cache_dir="/exp/tianteng.gu/huggingface/cache"  # 缓存目录
    ).select(selected_ids)  # 选择指定的样本

    # 创建Whisper处理器，用于音频预处理和文本后处理
    processor = WhisperProcessor.from_pretrained("openai/whisper-large-v3")

    # 定义映射函数，将音频转换为预测结果
    def map_to_pred(batch):
        # 获取音频数据
        audio = batch["audio"]

        # 使用处理器将音频转换为模型输入特征
        # audio["array"]是音频的数组表示，sampling_rate是采样率
        # return_tensors="pt"表示返回PyTorch张量
        input_features = processor(
            audio["array"],
            sampling_rate=audio["sampling_rate"],
            return_tensors="pt"
        ).input_features

        # 标准化参考文本（真实转录）
        # _normalize方法用于文本标准化（去除标点、统一大小写等）
        batch["reference"] = processor.tokenizer._normalize(batch['text'])

        # 使用torch.no_grad()确保不计算梯度
        with torch.no_grad():
            # 使用模型生成预测的token ID
            # [0]表示取第一个（也是唯一一个）样本的结果
            predicted_ids = model.generate(input_features.to(device))[0]

        # 将预测的token ID解码为文本
        transcription = processor.decode(predicted_ids)

        # 标准化预测文本
        batch["prediction"] = processor.tokenizer._normalize(transcription)

        # 返回包含参考文本和预测文本的批次
        return batch

    # 将映射函数应用到整个数据集
    # map方法会对数据集中的每个样本调用map_to_pred函数
    result = librispeech_test_clean.map(map_to_pred)

    # 加载词错误率(WER)评估指标
    # WER是语音识别任务的标准评估指标
    wer = load("wer")

    # 计算并打印WER结果
    # wer.compute计算参考文本和预测文本之间的词错误率
    # 乘以100转换为百分比形式
    print(100 * wer.compute(references=result["reference"], predictions=result["prediction"]))

# 主程序入口：当脚本直接运行时执行以下代码
# __name__ == '__main__' 是Python的标准用法，确保代码只在直接运行脚本时执行
if __name__ == '__main__':
    # 导入命令行参数解析库
    import argparse
    # 导入Whisper数据处理工具（*表示导入所有公开函数）
    from datautils_whisper import *

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser()

    # 添加模型参数：指定要加载的Whisper模型
    parser.add_argument(
        "--model",
        type=str,  # 参数类型为字符串
        help="whisper model to load"  # 参数说明
    )

    # 添加数据集参数：指定校准数据的来源
    parser.add_argument(
        "--dataset",
        type=str,
        help="Where to extract calibration data from.",
    )

    # 添加随机种子参数：用于数据采样的随机种子
    parser.add_argument(
        "--seed", type=int, default=0, help="Seed for sampling the calibration data."
    )

    # 添加样本数量参数：校准数据的样本数量
    parser.add_argument(
        "--nsamples", type=int, default=128, help="Number of calibration data samples."
    )

    # 添加阻尼参数：用于数值稳定性的阻尼百分比
    parser.add_argument(
        "--percdamp",
        type=float,  # 浮点数类型
        default=0.01,  # 默认值1%
        help="Percent of the average Hessian diagonal to use for dampening.",
    )

    # 添加编码器稀疏度参数：编码器的目标稀疏度
    parser.add_argument("--encoder_sparsity", type=float, default=0, help="Target encoder sparsity")

    # 添加解码器稀疏度参数：解码器的目标稀疏度
    parser.add_argument("--decoder_sparsity", type=float, default=0, help="Target decoder sparsity")

    # 添加N:M剪枝的N参数
    parser.add_argument("--prunen", type=int, default=0, help="N for N:M pruning.")

    # 添加N:M剪枝的M参数
    parser.add_argument("--prunem", type=int, default=0, help="M for N:M pruning.")

    # 添加块大小参数：用于自适应掩码选择的块大小
    parser.add_argument(
        "--blocksize",
        type=int,
        default=128,
        help="Blocksize to use for adaptive mask selection.",
    )

    # 添加GMP基线参数：是否运行梯度幅度剪枝基线
    parser.add_argument(
        "--gmp", action="store_true", help="Whether to run the GMP baseline."
    )

    # 添加量化位数参数：是否同时进行量化
    parser.add_argument(
        "--wbits", type=int, default=16, help="Whether to quantize as well."
    )

    # 添加最小层参数：剪枝ID大于等于此值的所有层
    parser.add_argument(
        "--minlayer", type=int, default=-1, help="Prune all layers with id >= this."
    )

    # 添加最大层参数：剪枝ID小于此值的所有层
    parser.add_argument(
        "--maxlayer", type=int, default=1000, help="Prune all layers with id < this."
    )

    # 添加仅剪枝参数：只剪枝包含此文本的层
    parser.add_argument(
        "--prune_only",
        type=str,
        default="",
        help="Prune only layers that contain this text.",
    )

    # 添加反转参数：反转子集选择
    parser.add_argument("--invert", action="store_true", help="Invert subset.")

    # 添加真正顺序参数：是否使用真正的顺序模式运行
    parser.add_argument(
        "--true-sequential",
        action="store_true",  # 布尔标志，存在即为True
        help="Whether to run in true sequential model.",
    )

    # 添加wandb日志参数：是否记录到wandb
    parser.add_argument(
        "--log_wandb", action="store_true", help="Whether to log to wandb."
    )

    # 添加评估参数：是否进行评估
    parser.add_argument(
        '--eval', type=str, default="false",
        help='whether to evaluate.'
    )

    # 添加保存参数：保存模型的路径
    parser.add_argument(
        "--save",
        default="",
        help="Path to saved model."
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 加载Whisper模型
    model = get_whisper(args.model)
    # 将模型设置为评估模式（禁用dropout等训练特有的层）
    model.eval()

    # 获取数据加载器
    # 根据指定的数据集、样本数、随机种子和模型ID创建数据加载器
    dataloader = get_loaders(
        args.dataset, nsamples=args.nsamples, seed=args.seed, model_id=args.model
    )

    # 设置设备为第一个CUDA GPU
    DEV = torch.device("cuda:0")

    # 记录开始时间
    tick = time.time()

    # 执行Whisper模型的顺序剪枝
    whisper_sequential(model, dataloader, DEV)

    # 注释掉的代码：用于检查模型参数的稀疏度
    # for n, p in model.named_parameters():
    #     print(n, torch.mean((p == 0).float()))
    #     if 'down_proj' in n:
    #         break

    # 打印剪枝所用的总时间
    print(time.time() - tick)

    # 如果指定了评估，则进行模型评估
    if args.eval == "true":
        whisper_eval(model, DEV)

    # 如果指定了保存路径，则保存模型
    if args.save != "":
        # 如果保存目录不存在，则创建目录
        if not os.path.exists(args.save):
            os.makedirs(args.save)

        # 保存预训练模型
        model.save_pretrained(args.save)

        # 导入并保存对应的分词器
        from transformers import AutoTokenizer
        # 加载分词器（use_fast=False使用慢速分词器以确保兼容性）
        tokenizer = AutoTokenizer.from_pretrained(args.model, use_fast=False)
        # 保存分词器到同一目录
        tokenizer.save_pretrained(args.save)