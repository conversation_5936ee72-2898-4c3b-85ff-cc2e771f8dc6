# CUDA_VISIBLE_DEVICES=0

model=openai/whisper-large-v3
LOGFILE=/exp/tianteng.gu/projects/sparsegpt-main/exp/log-0.5

/home/<USER>/miniconda3/envs/sparse/bin/python /exp/tianteng.gu/projects/sparsegpt-master/whisper_errors_precise.py  \
    --model $model \
    --dataset libri_other \
    --nsamples 2048 \
    --blocksize 256 \
    --encoder_sparsity 0.6 \
    --decoder_sparsity 0.6 \
    --language en \
    --task transcribe
