decoder:
- encoder_attn.k_proj: 16.875
  encoder_attn.out_proj: 411.0
  encoder_attn.q_proj: 7.64453125
  encoder_attn.v_proj: 14.3046875
  fc1: 29.203125
  fc2: 8.3359375
  self_attn.k_proj: 32.53125
  self_attn.out_proj: 3.21484375
  self_attn.q_proj: 29.609375
  self_attn.v_proj: 8.703125
- encoder_attn.k_proj: 18.875
  encoder_attn.out_proj: 165.75
  encoder_attn.q_proj: 1.259765625
  encoder_attn.v_proj: 14.8984375
  fc1: 8.0
  fc2: 2.3359375
  self_attn.k_proj: 2.798828125
  self_attn.out_proj: 0.0751953125
  self_attn.q_proj: 3.00390625
  self_attn.v_proj: 0.62548828125
- encoder_attn.k_proj: 10.6171875
  encoder_attn.out_proj: 41.1875
  encoder_attn.q_proj: 1.001953125
  encoder_attn.v_proj: 9.046875
  fc1: 16.296875
  fc2: 0.94384765625
  self_attn.k_proj: 0.77734375
  self_attn.out_proj: 0.06610107421875
  self_attn.q_proj: 1.6455078125
  self_attn.v_proj: 0.308837890625
- encoder_attn.k_proj: 9.0546875
  encoder_attn.out_proj: 69.3125
  encoder_attn.q_proj: 0.066650390625
  encoder_attn.v_proj: 6.35546875
  fc1: 8.0
  fc2: 1.5185546875
  self_attn.k_proj: 0.2763671875
  self_attn.out_proj: 0.0625
  self_attn.q_proj: 0.51025390625
  self_attn.v_proj: 0.291015625
- encoder_attn.k_proj: 17.40625
  encoder_attn.out_proj: 154.25
  encoder_attn.q_proj: 0.95458984375
  encoder_attn.v_proj: 14.46875
  fc1: 7.95703125
  fc2: 1.3984375
  self_attn.k_proj: 0.53125
  self_attn.out_proj: 0.07958984375
  self_attn.q_proj: 1.8388671875
  self_attn.v_proj: 0.37451171875
- encoder_attn.k_proj: 15.0
  encoder_attn.out_proj: 221.375
  encoder_attn.q_proj: 1.2626953125
  encoder_attn.v_proj: 11.1796875
  fc1: 16.0
  fc2: 0.297607421875
  self_attn.k_proj: 0.92431640625
  self_attn.out_proj: 0.447021484375
  self_attn.q_proj: 1.3134765625
  self_attn.v_proj: 0.49560546875
- encoder_attn.k_proj: 24.828125
  encoder_attn.out_proj: 218.75
  encoder_attn.q_proj: 1.60546875
  encoder_attn.v_proj: 18.734375
  fc1: 19.0625
  fc2: 0.311767578125
  self_attn.k_proj: 1.41015625
  self_attn.out_proj: 0.376220703125
  self_attn.q_proj: 1.69921875
  self_attn.v_proj: 0.6279296875
- encoder_attn.k_proj: 18.4375
  encoder_attn.out_proj: 278.0
  encoder_attn.q_proj: 2.552734375
  encoder_attn.v_proj: 15.203125
  fc1: 21.578125
  fc2: 0.220703125
  self_attn.k_proj: 1.931640625
  self_attn.out_proj: 0.6181640625
  self_attn.q_proj: 1.8662109375
  self_attn.v_proj: 0.57861328125
- encoder_attn.k_proj: 15.1015625
  encoder_attn.out_proj: 167.75
  encoder_attn.q_proj: 1.4833984375
  encoder_attn.v_proj: 10.1875
  fc1: 42.25
  fc2: 0.1671142578125
  self_attn.k_proj: 2.27734375
  self_attn.out_proj: 0.5
  self_attn.q_proj: 1.9775390625
  self_attn.v_proj: 0.67529296875
- encoder_attn.k_proj: 16.0
  encoder_attn.out_proj: 406.5
  encoder_attn.q_proj: 2.611328125
  encoder_attn.v_proj: 13.859375
  fc1: 40.1875
  fc2: 0.267333984375
  self_attn.k_proj: 2.046875
  self_attn.out_proj: 0.37451171875
  self_attn.q_proj: 2.208984375
  self_attn.v_proj: 0.58349609375
- encoder_attn.k_proj: 24.171875
  encoder_attn.out_proj: 222.0
  encoder_attn.q_proj: 2.302734375
  encoder_attn.v_proj: 14.234375
  fc1: 41.28125
  fc2: 0.1490478515625
  self_attn.k_proj: 3.294921875
  self_attn.out_proj: 0.53759765625
  self_attn.q_proj: 2.912109375
  self_attn.v_proj: 0.95361328125
- encoder_attn.k_proj: 24.21875
  encoder_attn.out_proj: 450.5
  encoder_attn.q_proj: 0.9892578125
  encoder_attn.v_proj: 16.96875
  fc1: 34.8125
  fc2: 0.1915283203125
  self_attn.k_proj: 2.5234375
  self_attn.out_proj: 0.373046875
  self_attn.q_proj: 2.646484375
  self_attn.v_proj: 0.794921875
- encoder_attn.k_proj: 25.59375
  encoder_attn.out_proj: 171.0
  encoder_attn.q_proj: 1.92578125
  encoder_attn.v_proj: 20.15625
  fc1: 22.84375
  fc2: 0.30419921875
  self_attn.k_proj: 3.0234375
  self_attn.out_proj: 0.9423828125
  self_attn.q_proj: 3.1796875
  self_attn.v_proj: 1.197265625
- encoder_attn.k_proj: 28.65625
  encoder_attn.out_proj: 508.25
  encoder_attn.q_proj: 1.80859375
  encoder_attn.v_proj: 19.859375
  fc1: 15.6484375
  fc2: 0.7353515625
  self_attn.k_proj: 2.79296875
  self_attn.out_proj: 0.6474609375
  self_attn.q_proj: 2.5078125
  self_attn.v_proj: 1.2421875
- encoder_attn.k_proj: 28.390625
  encoder_attn.out_proj: 355.25
  encoder_attn.q_proj: 1.0
  encoder_attn.v_proj: 20.828125
  fc1: 18.0
  fc2: 0.3583984375
  self_attn.k_proj: 2.6640625
  self_attn.out_proj: 0.5
  self_attn.q_proj: 2.5859375
  self_attn.v_proj: 0.98974609375
- encoder_attn.k_proj: 30.15625
  encoder_attn.out_proj: 390.0
  encoder_attn.q_proj: 1.322265625
  encoder_attn.v_proj: 22.046875
  fc1: 16.0625
  fc2: 0.490234375
  self_attn.k_proj: 3.18359375
  self_attn.out_proj: 0.83642578125
  self_attn.q_proj: 3.423828125
  self_attn.v_proj: 1.4140625
- encoder_attn.k_proj: 31.328125
  encoder_attn.out_proj: 836.5
  encoder_attn.q_proj: 1.017578125
  encoder_attn.v_proj: 23.796875
  fc1: 17.640625
  fc2: 0.371337890625
  self_attn.k_proj: 2.615234375
  self_attn.out_proj: 0.60546875
  self_attn.q_proj: 2.619140625
  self_attn.v_proj: 1.2119140625
- encoder_attn.k_proj: 30.1875
  encoder_attn.out_proj: 552.0
  encoder_attn.q_proj: 1.4736328125
  encoder_attn.v_proj: 22.546875
  fc1: 15.71875
  fc2: 0.325927734375
  self_attn.k_proj: 2.267578125
  self_attn.out_proj: 1.0380859375
  self_attn.q_proj: 2.40234375
  self_attn.v_proj: 1.400390625
- encoder_attn.k_proj: 30.71875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 2.57421875
  encoder_attn.v_proj: 24.0
  fc1: 16.21875
  fc2: 0.327392578125
  self_attn.k_proj: 2.796875
  self_attn.out_proj: 0.5087890625
  self_attn.q_proj: 2.548828125
  self_attn.v_proj: 1.4345703125
- encoder_attn.k_proj: 32.0
  encoder_attn.out_proj: 696.0
  encoder_attn.q_proj: 1.9541015625
  encoder_attn.v_proj: 26.359375
  fc1: 21.015625
  fc2: 0.3701171875
  self_attn.k_proj: 2.099609375
  self_attn.out_proj: 0.370849609375
  self_attn.q_proj: 1.970703125
  self_attn.v_proj: 1.0
- encoder_attn.k_proj: 33.21875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 2.740234375
  encoder_attn.v_proj: 25.90625
  fc1: 14.9296875
  fc2: 0.406982421875
  self_attn.k_proj: 3.4453125
  self_attn.out_proj: 0.82666015625
  self_attn.q_proj: 2.6484375
  self_attn.v_proj: 1.2578125
- encoder_attn.k_proj: 33.59375
  encoder_attn.out_proj: 604.5
  encoder_attn.q_proj: 2.19140625
  encoder_attn.v_proj: 25.421875
  fc1: 17.53125
  fc2: 0.37548828125
  self_attn.k_proj: 2.009765625
  self_attn.out_proj: 1.30859375
  self_attn.q_proj: 2.123046875
  self_attn.v_proj: 1.1171875
- encoder_attn.k_proj: 31.609375
  encoder_attn.out_proj: 665.5
  encoder_attn.q_proj: 2.841796875
  encoder_attn.v_proj: 29.53125
  fc1: 11.6640625
  fc2: 0.1875
  self_attn.k_proj: 3.986328125
  self_attn.out_proj: 0.438720703125
  self_attn.q_proj: 2.91015625
  self_attn.v_proj: 1.4716796875
- encoder_attn.k_proj: 35.40625
  encoder_attn.out_proj: 779.5
  encoder_attn.q_proj: 2.455078125
  encoder_attn.v_proj: 26.734375
  fc1: 10.0234375
  fc2: 0.48876953125
  self_attn.k_proj: 2.013671875
  self_attn.out_proj: 0.541015625
  self_attn.q_proj: 1.935546875
  self_attn.v_proj: 0.982421875
- encoder_attn.k_proj: 32.3125
  encoder_attn.out_proj: 640.5
  encoder_attn.q_proj: 1.9091796875
  encoder_attn.v_proj: 25.859375
  fc1: 7.80859375
  fc2: 0.447265625
  self_attn.k_proj: 2.185546875
  self_attn.out_proj: 1.01953125
  self_attn.q_proj: 1.791015625
  self_attn.v_proj: 0.8955078125
- encoder_attn.k_proj: 34.65625
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.482421875
  encoder_attn.v_proj: 27.65625
  fc1: 10.0859375
  fc2: 0.509765625
  self_attn.k_proj: 2.095703125
  self_attn.out_proj: 0.61962890625
  self_attn.q_proj: 2.0
  self_attn.v_proj: 1.1494140625
- encoder_attn.k_proj: 36.8125
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.79296875
  encoder_attn.v_proj: 25.171875
  fc1: 9.8203125
  fc2: 0.368408203125
  self_attn.k_proj: 2.208984375
  self_attn.out_proj: 0.42431640625
  self_attn.q_proj: 1.986328125
  self_attn.v_proj: 0.9892578125
- encoder_attn.k_proj: 36.375
  encoder_attn.out_proj: 754.5
  encoder_attn.q_proj: 1.43359375
  encoder_attn.v_proj: 29.359375
  fc1: 11.359375
  fc2: 0.41796875
  self_attn.k_proj: 2.037109375
  self_attn.out_proj: 0.50390625
  self_attn.q_proj: 2.05078125
  self_attn.v_proj: 1.1591796875
- encoder_attn.k_proj: 37.375
  encoder_attn.out_proj: 793.5
  encoder_attn.q_proj: 1.390625
  encoder_attn.v_proj: 27.171875
  fc1: 13.109375
  fc2: 0.441162109375
  self_attn.k_proj: 2.001953125
  self_attn.out_proj: 1.0
  self_attn.q_proj: 2.265625
  self_attn.v_proj: 0.92041015625
- encoder_attn.k_proj: 36.9375
  encoder_attn.out_proj: 661.0
  encoder_attn.q_proj: 1.365234375
  encoder_attn.v_proj: 25.609375
  fc1: 14.328125
  fc2: 0.44482421875
  self_attn.k_proj: 2.5390625
  self_attn.out_proj: 0.48876953125
  self_attn.q_proj: 2.34375
  self_attn.v_proj: 1.0
- encoder_attn.k_proj: 39.875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.796875
  encoder_attn.v_proj: 28.015625
  fc1: 11.6953125
  fc2: 0.5205078125
  self_attn.k_proj: 2.865234375
  self_attn.out_proj: 0.69384765625
  self_attn.q_proj: 2.537109375
  self_attn.v_proj: 1.3974609375
- encoder_attn.k_proj: 41.71875
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 1.1298828125
  encoder_attn.v_proj: 30.5
  fc1: 9.0546875
  fc2: 0.92919921875
  self_attn.k_proj: 2.267578125
  self_attn.out_proj: 0.6455078125
  self_attn.q_proj: 2.650390625
  self_attn.v_proj: 0.716796875
encoder:
- fc1: .inf
  fc2: 0.2783203125
  self_attn.k_proj: 4.36328125
  self_attn.out_proj: 0.371337890625
  self_attn.q_proj: .inf
  self_attn.v_proj: 3.69140625
- fc1: 3.8203125
  fc2: 0.052642822265625
  self_attn.k_proj: 1.1123046875
  self_attn.out_proj: 0.138427734375
  self_attn.q_proj: 0.96875
  self_attn.v_proj: 0.4931640625
- fc1: 4.5625
  fc2: 0.0147247314453125
  self_attn.k_proj: 0.833984375
  self_attn.out_proj: 0.1422119140625
  self_attn.q_proj: 0.73583984375
  self_attn.v_proj: 0.465087890625
- fc1: .inf
  fc2: 0.0236358642578125
  self_attn.k_proj: 0.9521484375
  self_attn.out_proj: 0.106689453125
  self_attn.q_proj: 0.7470703125
  self_attn.v_proj: 0.488525390625
- fc1: 4.09375
  fc2: 0.0177001953125
  self_attn.k_proj: 0.94677734375
  self_attn.out_proj: 0.10601806640625
  self_attn.q_proj: 0.703125
  self_attn.v_proj: 0.49560546875
- fc1: .inf
  fc2: 0.0191802978515625
  self_attn.k_proj: 0.91064453125
  self_attn.out_proj: 0.1292724609375
  self_attn.q_proj: 0.6240234375
  self_attn.v_proj: 0.525390625
- fc1: .inf
  fc2: 0.0213623046875
  self_attn.k_proj: 1.2998046875
  self_attn.out_proj: 0.119384765625
  self_attn.q_proj: 0.9052734375
  self_attn.v_proj: 0.6298828125
- fc1: .inf
  fc2: 0.0321044921875
  self_attn.k_proj: 1.44140625
  self_attn.out_proj: 0.1302490234375
  self_attn.q_proj: 1.0341796875
  self_attn.v_proj: 0.66064453125
- fc1: .inf
  fc2: 0.046905517578125
  self_attn.k_proj: 1.484375
  self_attn.out_proj: 0.146240234375
  self_attn.q_proj: 1.08203125
  self_attn.v_proj: 0.68310546875
- fc1: .inf
  fc2: 0.036956787109375
  self_attn.k_proj: 1.390625
  self_attn.out_proj: 0.121826171875
  self_attn.q_proj: 0.974609375
  self_attn.v_proj: 0.67333984375
- fc1: .inf
  fc2: 0.043182373046875
  self_attn.k_proj: 1.146484375
  self_attn.out_proj: 0.15576171875
  self_attn.q_proj: 0.87060546875
  self_attn.v_proj: 0.6806640625
- fc1: .inf
  fc2: 0.0418701171875
  self_attn.k_proj: 1.3681640625
  self_attn.out_proj: 0.1441650390625
  self_attn.q_proj: 0.92822265625
  self_attn.v_proj: 0.67919921875
- fc1: .inf
  fc2: 0.06158447265625
  self_attn.k_proj: 1.369140625
  self_attn.out_proj: 0.142578125
  self_attn.q_proj: 0.9033203125
  self_attn.v_proj: 0.6201171875
- fc1: .inf
  fc2: 0.08221435546875
  self_attn.k_proj: 1.203125
  self_attn.out_proj: 0.1546630859375
  self_attn.q_proj: 0.83642578125
  self_attn.v_proj: 0.57666015625
- fc1: .inf
  fc2: 0.1044921875
  self_attn.k_proj: 1.3798828125
  self_attn.out_proj: 0.111083984375
  self_attn.q_proj: 0.953125
  self_attn.v_proj: 0.57177734375
- fc1: .inf
  fc2: 0.095947265625
  self_attn.k_proj: 1.2783203125
  self_attn.out_proj: 0.14697265625
  self_attn.q_proj: 0.91796875
  self_attn.v_proj: 0.57080078125
- fc1: .inf
  fc2: 0.11480712890625
  self_attn.k_proj: 1.1630859375
  self_attn.out_proj: 0.157470703125
  self_attn.q_proj: 0.76416015625
  self_attn.v_proj: 0.59814453125
- fc1: .inf
  fc2: 0.1363525390625
  self_attn.k_proj: 1.521484375
  self_attn.out_proj: 0.154541015625
  self_attn.q_proj: 1.1201171875
  self_attn.v_proj: 0.72314453125
- fc1: .inf
  fc2: 0.168701171875
  self_attn.k_proj: 1.5546875
  self_attn.out_proj: 0.1829833984375
  self_attn.q_proj: 1.0703125
  self_attn.v_proj: 0.7236328125
- fc1: .inf
  fc2: 0.204833984375
  self_attn.k_proj: 1.4658203125
  self_attn.out_proj: 0.1767578125
  self_attn.q_proj: 1.1806640625
  self_attn.v_proj: 0.72412109375
- fc1: .inf
  fc2: 0.253173828125
  self_attn.k_proj: 1.69921875
  self_attn.out_proj: 0.26171875
  self_attn.q_proj: 1.5830078125
  self_attn.v_proj: 0.85888671875
- fc1: .inf
  fc2: 0.2425537109375
  self_attn.k_proj: 1.7119140625
  self_attn.out_proj: 0.2149658203125
  self_attn.q_proj: 1.55859375
  self_attn.v_proj: 0.87646484375
- fc1: .inf
  fc2: 0.2509765625
  self_attn.k_proj: 1.9775390625
  self_attn.out_proj: 0.280029296875
  self_attn.q_proj: 1.9580078125
  self_attn.v_proj: 0.9775390625
- fc1: .inf
  fc2: 0.26318359375
  self_attn.k_proj: 1.9189453125
  self_attn.out_proj: 0.242919921875
  self_attn.q_proj: 1.9267578125
  self_attn.v_proj: 1.0166015625
- fc1: .inf
  fc2: 0.27978515625
  self_attn.k_proj: 2.267578125
  self_attn.out_proj: 0.278076171875
  self_attn.q_proj: 2.14453125
  self_attn.v_proj: 1.12890625
- fc1: .inf
  fc2: 0.3076171875
  self_attn.k_proj: 2.173828125
  self_attn.out_proj: 0.267578125
  self_attn.q_proj: 2.068359375
  self_attn.v_proj: 1.0849609375
- fc1: .inf
  fc2: 0.3134765625
  self_attn.k_proj: 2.3359375
  self_attn.out_proj: 0.2529296875
  self_attn.q_proj: 2.2265625
  self_attn.v_proj: 1.1884765625
- fc1: .inf
  fc2: 0.35400390625
  self_attn.k_proj: 2.228515625
  self_attn.out_proj: 0.200927734375
  self_attn.q_proj: 1.955078125
  self_attn.v_proj: 1.1123046875
- fc1: .inf
  fc2: 0.419189453125
  self_attn.k_proj: 2.37890625
  self_attn.out_proj: 0.326904296875
  self_attn.q_proj: 2.185546875
  self_attn.v_proj: 1.30859375
- fc1: .inf
  fc2: 0.4560546875
  self_attn.k_proj: 2.70703125
  self_attn.out_proj: 0.42236328125
  self_attn.q_proj: 2.646484375
  self_attn.v_proj: 1.580078125
- fc1: .inf
  fc2: 0.52734375
  self_attn.k_proj: 2.609375
  self_attn.out_proj: 0.445068359375
  self_attn.q_proj: 2.59375
  self_attn.v_proj: 1.642578125
- fc1: .inf
  fc2: 0.59765625
  self_attn.k_proj: 2.765625
  self_attn.out_proj: 0.499755859375
  self_attn.q_proj: 2.83984375
  self_attn.v_proj: 1.78125
