import numpy as np
import random
import torch
from datasets import load_dataset, Audio
from transformers import AutoProcessor

def set_seed(seed):
    """设置随机种子以确保实验可重复性
    参数：
        seed (int): 随机种子值，用于控制随机数生成器的初始化状态
    """
    np.random.seed(seed)        # 设置numpy的随机种子
    torch.random.manual_seed(seed)  # 设置PyTorch的随机种子

def get_libri(nsamples, seed, model_id, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)
    random.seed(seed)
    dataset = load_dataset("/root/autodl-tmp/sparse-whisper/exp/huggingface/dataset", "clean", split="train.100", cache_dir="/root/autodl-tmp/sparse-whisper/exp/huggingface/cache")
    sample_index = random.sample(list(range(len(dataset))), nsamples)
    dataset = dataset.select(sample_index)
    trainloader = []
    for i in range(nsamples):
        sample = dataset[i]["audio"]
        input_features = processor(sample["array"], sampling_rate=sample["sampling_rate"], return_tensors="pt").input_features
        trainloader.append(input_features)
    return trainloader

def get_libri_other(nsamples, seed, model_id, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)
    random.seed(seed)
    dataset = load_dataset("/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr", "other", split="train.500", cache_dir="/exp/tianteng.gu/huggingface/cache")
    sample_index = random.sample(list(range(len(dataset))), nsamples)
    dataset = dataset.select(sample_index)
    trainloader = []
    for i in range(nsamples):
        sample = dataset[i]["audio"]
        input_features = processor(sample["array"], sampling_rate=sample["sampling_rate"], return_tensors="pt").input_features
        trainloader.append(input_features)
    return trainloader

def get_cv5(nsamples, seed, model_id, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)
    random.seed(seed)
    dataset = load_dataset("mozilla-foundation/common_voice_5_1", "en", split="train", use_auth_token=True, cache_dir="/exp/tianteng.gu/huggingface/cache")
    sample_index = random.sample(list(range(len(dataset))), nsamples)
    dataset = dataset.select(sample_index)
    dataset = dataset.cast_column("audio", Audio(sampling_rate=16000))
    trainloader = []
    for i in range(nsamples):
        sample = dataset[i]["audio"]
        input_features = processor(sample["array"], sampling_rate=16000, return_tensors="pt").input_features
        trainloader.append(input_features)
    return trainloader

def get_tedlium(nsamples, seed, model_id, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)
    random.seed(seed)
    dataset = load_dataset("LIUM/tedlium", "release3", split="train", cache_dir="/exp/tianteng.gu/huggingface/cache")
    sample_index = random.sample(list(range(len(dataset))), nsamples)
    dataset = dataset.select(sample_index)
    dataset = dataset.cast_column("audio", Audio(sampling_rate=16000))
    trainloader = []
    for i in range(nsamples):
        sample = dataset[i]["audio"]
        input_features = processor(sample["array"], sampling_rate=sample["sampling_rate"], return_tensors="pt").input_features
        trainloader.append(input_features)
    return trainloader

def get_mls(nsamples, seed, model_id, language, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)

    if language == "german":
        dataset = load_dataset("facebook/multilingual_librispeech", "german", split="train", cache_dir="/exp/tianteng.gu/huggingface/cache")
        random.seed(seed)
        sample_index = random.sample(list(range(len(dataset))), nsamples)
        dataset = dataset.select(sample_index)
        dataset = dataset.cast_column("audio", Audio(sampling_rate=16000))
        trainloader = []
        for i in range(nsamples):
            sample = dataset[i]["audio"]
            input_features = processor(sample["array"], sampling_rate=sample["sampling_rate"], return_tensors="pt").input_features
            trainloader.append(input_features)
    else:
        mls = load_dataset("facebook/multilingual_librispeech", language, split="train", streaming=True)

        shuffled_mls = mls.shuffle(seed=42, buffer_size=1000)
        
        trainloader = []
        count = 0
        for x in shuffled_mls:
            sample = x["audio"]
            input_features = processor(sample["array"], sampling_rate=16000, return_tensors="pt").input_features
            trainloader.append(input_features)
            count += 1
            if count == nsamples:
                break
    return trainloader

def get_libri_other_text(nsamples, seed, model_id, dtype=torch.float16):
    processor = AutoProcessor.from_pretrained(model_id)
    processor.tokenizer.set_prefix_tokens(task="transcribe", language="english")
    random.seed(seed)
    dataset = load_dataset("/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr", "other", split="train.500", cache_dir="/exp/tianteng.gu/huggingface/cache")
    sample_index = random.sample(list(range(len(dataset))), nsamples)
    dataset = dataset.select(sample_index)
    trainloader = []
    text_labels = []
    for i in range(nsamples):
        sample = dataset[i]["audio"]
        input_features = processor(sample["array"], sampling_rate=sample["sampling_rate"], return_tensors="pt").input_features
        trainloader.append(input_features)
        input_ids = processor.tokenizer(dataset[i]['text']).input_ids
        text_labels.append(input_ids)
    return trainloader, text_labels

def get_loaders(dataset, nsamples, seed, model_id, language=None, dtype=torch.float16):
    if dataset == "libri":
        return get_libri(nsamples, seed, model_id, dtype=dtype)
    elif dataset == "libri_other":
        return get_libri_other(nsamples, seed, model_id, dtype=dtype)
    elif dataset == "cv5":
        return get_cv5(nsamples, seed, model_id, dtype=dtype)
    elif dataset == "mix":
        number = int(nsamples / 4)
        dataloader = []
        dataloader += get_libri(number, seed, model_id, dtype=dtype)
        dataloader += get_libri_other(number, seed, model_id, dtype=dtype)
        dataloader += get_cv5(number, seed, model_id, dtype=dtype)
        dataloader += get_tedlium(number, seed, model_id, dtype=dtype)
        return dataloader
    elif dataset == "mls":
        return get_mls(nsamples, seed, model_id, language, dtype=dtype)
    elif dataset == "libri_other_text":
        return get_libri_other_text(nsamples, seed, model_id, dtype=dtype)
    
    


    