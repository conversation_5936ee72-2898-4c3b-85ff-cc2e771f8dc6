{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import yaml\n", "\n", "all_errors = {}\n", "\n", "# 指定文件夹路径\n", "folder_path = '/exp/tianteng.gu/projects/sparsegpt-master/whisper_error'\n", "\n", "# 使用glob模块找到所有YAML文件\n", "yaml_files = glob.glob(os.path.join(folder_path, '*.yaml'))\n", "\n", "# # 遍历所有YAML文件\n", "for file_path in yaml_files:\n", "    with open(file_path, 'r') as file:\n", "        yaml_content = yaml.safe_load(file)\n", "        s = float(os.path.splitext(file_path)[0].split('/')[-1])\n", "        all_errors[s] = yaml_content\n", "\n", "all_errors = dict(sorted(all_errors.items(), key=lambda item: item[0]))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'decoder': [{'encoder_attn.k_proj': 1.7139029502868652,\n", "   'encoder_attn.out_proj': 0.00047242094296962023,\n", "   'encoder_attn.q_proj': 0.05682507902383804,\n", "   'encoder_attn.v_proj': 1.325894832611084,\n", "   'fc1': 6.322543144226074,\n", "   'fc2': 0.004047955386340618,\n", "   'self_attn.k_proj': 3.1317639350891113,\n", "   'self_attn.out_proj': 0.01988299749791622,\n", "   'self_attn.q_proj': 2.0411081314086914,\n", "   'self_attn.v_proj': 0.3720465302467346},\n", "  {'encoder_attn.k_proj': 1.5999035835266113,\n", "   'encoder_attn.out_proj': 0.0016350034857168794,\n", "   'encoder_attn.q_proj': 0.08136662095785141,\n", "   'encoder_attn.v_proj': 1.263113021850586,\n", "   'fc1': 4.9690327644348145,\n", "   'fc2': 0.001033166190609336,\n", "   'self_attn.k_proj': 0.1999412328004837,\n", "   'self_attn.out_proj': 5.283519931253977e-05,\n", "   'self_attn.q_proj': 0.14476506412029266,\n", "   'self_attn.v_proj': 0.01368621177971363},\n", "  {'encoder_attn.k_proj': 0.5482765436172485,\n", "   'encoder_attn.out_proj': 0.00024006760213524103,\n", "   'encoder_attn.q_proj': 0.049130529165267944,\n", "   'encoder_attn.v_proj': 0.743657648563385,\n", "   'fc1': 4.062978267669678,\n", "   'fc2': 0.0016197982477024198,\n", "   'self_attn.k_proj': 0.06582121551036835,\n", "   'self_attn.out_proj': 4.077922130818479e-05,\n", "   'self_attn.q_proj': 0.04466237872838974,\n", "   'self_attn.v_proj': 0.0056266882456839085},\n", "  {'encoder_attn.k_proj': 0.2722093164920807,\n", "   'encoder_attn.out_proj': 1.5581123079755343e-05,\n", "   'encoder_attn.q_proj': 0.0020331554114818573,\n", "   'encoder_attn.v_proj': 0.4883236289024353,\n", "   'fc1': 2.86045503616333,\n", "   'fc2': 0.0009465941693633795,\n", "   'self_attn.k_proj': 0.005354694090783596,\n", "   'self_attn.out_proj': 6.1669225033256225e-06,\n", "   'self_attn.q_proj': 0.00419531250372529,\n", "   'self_attn.v_proj': 0.00028819029103033245},\n", "  {'encoder_attn.k_proj': 0.9395433068275452,\n", "   'encoder_attn.out_proj': 0.0007241348503157496,\n", "   'encoder_attn.q_proj': 0.24504044651985168,\n", "   'encoder_attn.v_proj': 0.9710937738418579,\n", "   'fc1': 3.6211225986480713,\n", "   'fc2': 0.0006456320988945663,\n", "   'self_attn.k_proj': 0.011522066779434681,\n", "   'self_attn.out_proj': 7.86134842201136e-05,\n", "   'self_attn.q_proj': 0.012282649986445904,\n", "   'self_attn.v_proj': 0.0019663574639707804},\n", "  {'encoder_attn.k_proj': 0.7143014669418335,\n", "   'encoder_attn.out_proj': 0.0011590453796088696,\n", "   'encoder_attn.q_proj': 0.130802184343338,\n", "   'encoder_attn.v_proj': 0.9932019114494324,\n", "   'fc1': 6.664170265197754,\n", "   'fc2': 0.0007806645007804036,\n", "   'self_attn.k_proj': 0.1504378318786621,\n", "   'self_attn.out_proj': 0.0010118482168763876,\n", "   'self_attn.q_proj': 0.20337441563606262,\n", "   'self_attn.v_proj': 0.06471472233533859},\n", "  {'encoder_attn.k_proj': 1.6376888751983643,\n", "   'encoder_attn.out_proj': 0.004119296558201313,\n", "   'encoder_attn.q_proj': 0.4854700565338135,\n", "   'encoder_attn.v_proj': 1.2954325675964355,\n", "   'fc1': 8.812592506408691,\n", "   'fc2': 0.007553170435130596,\n", "   'self_attn.k_proj': 0.3698909878730774,\n", "   'self_attn.out_proj': 0.0035569665487855673,\n", "   'self_attn.q_proj': 0.45338815450668335,\n", "   'self_attn.v_proj': 0.1546097993850708},\n", "  {'encoder_attn.k_proj': 2.056180953979492,\n", "   'encoder_attn.out_proj': 0.002257276326417923,\n", "   'encoder_attn.q_proj': 0.2232796847820282,\n", "   'encoder_attn.v_proj': 1.6211260557174683,\n", "   'fc1': 10.717967987060547,\n", "   'fc2': 0.006187151186168194,\n", "   'self_attn.k_proj': 0.5152088403701782,\n", "   'self_attn.out_proj': 0.009112248197197914,\n", "   'self_attn.q_proj': 0.5691444873809814,\n", "   'self_attn.v_proj': 0.21918267011642456},\n", "  {'encoder_attn.k_proj': 0.877600908279419,\n", "   'encoder_attn.out_proj': 0.0002890988835133612,\n", "   'encoder_attn.q_proj': 0.1353335678577423,\n", "   'encoder_attn.v_proj': 0.9275760650634766,\n", "   'fc1': 12.632108688354492,\n", "   'fc2': 0.011686723679304123,\n", "   'self_attn.k_proj': 0.8307597041130066,\n", "   'self_attn.out_proj': 0.007915832102298737,\n", "   'self_attn.q_proj': 0.8676849007606506,\n", "   'self_attn.v_proj': 0.28783878684043884},\n", "  {'encoder_attn.k_proj': 1.633552074432373,\n", "   'encoder_attn.out_proj': 0.0006609692936763167,\n", "   'encoder_attn.q_proj': 0.20354042947292328,\n", "   'encoder_attn.v_proj': 1.475052833557129,\n", "   'fc1': 16.2789249420166,\n", "   'fc2': 0.016817059367895126,\n", "   'self_attn.k_proj': 0.6606558561325073,\n", "   'self_attn.out_proj': 0.006773824337869883,\n", "   'self_attn.q_proj': 0.7861807942390442,\n", "   'self_attn.v_proj': 0.2784101366996765},\n", "  {'encoder_attn.k_proj': 2.0938053131103516,\n", "   'encoder_attn.out_proj': 0.004123668652027845,\n", "   'encoder_attn.q_proj': 0.8095401525497437,\n", "   'encoder_attn.v_proj': 1.60716712474823,\n", "   'fc1': 16.806312561035156,\n", "   'fc2': 0.020532358437776566,\n", "   'self_attn.k_proj': 1.7916676998138428,\n", "   'self_attn.out_proj': 0.011736718937754631,\n", "   'self_attn.q_proj': 1.9563517570495605,\n", "   'self_attn.v_proj': 0.7036205530166626},\n", "  {'encoder_attn.k_proj': 3.1135988235473633,\n", "   'encoder_attn.out_proj': 0.0018688462441787124,\n", "   'encoder_attn.q_proj': 0.41374367475509644,\n", "   'encoder_attn.v_proj': 1.8791532516479492,\n", "   'fc1': 16.640735626220703,\n", "   'fc2': 0.021073099225759506,\n", "   'self_attn.k_proj': 1.4357867240905762,\n", "   'self_attn.out_proj': 0.01062968373298645,\n", "   'self_attn.q_proj': 1.52657949924469,\n", "   'self_attn.v_proj': 0.5471910834312439},\n", "  {'encoder_attn.k_proj': 3.2663354873657227,\n", "   'encoder_attn.out_proj': 0.007616289425641298,\n", "   'encoder_attn.q_proj': 1.2815256118774414,\n", "   'encoder_attn.v_proj': 1.9583146572113037,\n", "   'fc1': 16.17620086669922,\n", "   'fc2': 0.03395315259695053,\n", "   'self_attn.k_proj': 2.966165065765381,\n", "   'self_attn.out_proj': 0.013457048684358597,\n", "   'self_attn.q_proj': 3.0526843070983887,\n", "   'self_attn.v_proj': 1.0184807777404785},\n", "  {'encoder_attn.k_proj': 3.651711940765381,\n", "   'encoder_attn.out_proj': 0.008732282556593418,\n", "   'encoder_attn.q_proj': 1.0032687187194824,\n", "   'encoder_attn.v_proj': 2.2369132041931152,\n", "   'fc1': 16.825929641723633,\n", "   'fc2': 0.05228555575013161,\n", "   'self_attn.k_proj': 3.6821413040161133,\n", "   'self_attn.out_proj': 0.01512949075549841,\n", "   'self_attn.q_proj': 3.9618775844573975,\n", "   'self_attn.v_proj': 1.4640061855316162},\n", "  {'encoder_attn.k_proj': 3.7874984741210938,\n", "   'encoder_attn.out_proj': 0.007134116254746914,\n", "   'encoder_attn.q_proj': 0.5455835461616516,\n", "   'encoder_attn.v_proj': 2.171518325805664,\n", "   'fc1': 17.00339698791504,\n", "   'fc2': 0.057321708649396896,\n", "   'self_attn.k_proj': 2.5705184936523438,\n", "   'self_attn.out_proj': 0.013452831655740738,\n", "   'self_attn.q_proj': 2.8454504013061523,\n", "   'self_attn.v_proj': 1.0791563987731934},\n", "  {'encoder_attn.k_proj': 3.9377853870391846,\n", "   'encoder_attn.out_proj': 0.006127803586423397,\n", "   'encoder_attn.q_proj': 0.7918739318847656,\n", "   'encoder_attn.v_proj': 2.128788948059082,\n", "   'fc1': 17.328018188476562,\n", "   'fc2': 0.06412191689014435,\n", "   'self_attn.k_proj': 4.32480525970459,\n", "   'self_attn.out_proj': 0.022094745188951492,\n", "   'self_attn.q_proj': 4.3174872398376465,\n", "   'self_attn.v_proj': 1.7792508602142334},\n", "  {'encoder_attn.k_proj': 4.060672760009766,\n", "   'encoder_attn.out_proj': 0.004905226640403271,\n", "   'encoder_attn.q_proj': 0.8217897415161133,\n", "   'encoder_attn.v_proj': 2.3507988452911377,\n", "   'fc1': 16.88031005859375,\n", "   'fc2': 0.06075286120176315,\n", "   'self_attn.k_proj': 3.804647207260132,\n", "   'self_attn.out_proj': 0.027795929461717606,\n", "   'self_attn.q_proj': 3.804410696029663,\n", "   'self_attn.v_proj': 1.5175573825836182},\n", "  {'encoder_attn.k_proj': 4.002291202545166,\n", "   'encoder_attn.out_proj': 0.006314863450825214,\n", "   'encoder_attn.q_proj': 0.8694868087768555,\n", "   'encoder_attn.v_proj': 2.271334648132324,\n", "   'fc1': 16.244144439697266,\n", "   'fc2': 0.06438297778367996,\n", "   'self_attn.k_proj': 3.0424752235412598,\n", "   'self_attn.out_proj': 0.016538936644792557,\n", "   'self_attn.q_proj': 3.0684823989868164,\n", "   'self_attn.v_proj': 1.2733290195465088},\n", "  {'encoder_attn.k_proj': 4.267663955688477,\n", "   'encoder_attn.out_proj': 0.005670615471899509,\n", "   'encoder_attn.q_proj': 0.891211986541748,\n", "   'encoder_attn.v_proj': 2.407259464263916,\n", "   'fc1': 16.835607528686523,\n", "   'fc2': 0.06404462456703186,\n", "   'self_attn.k_proj': 4.325374603271484,\n", "   'self_attn.out_proj': 0.02816598489880562,\n", "   'self_attn.q_proj': 4.432614803314209,\n", "   'self_attn.v_proj': 1.8400015830993652},\n", "  {'encoder_attn.k_proj': 4.275456428527832,\n", "   'encoder_attn.out_proj': 0.01375532615929842,\n", "   'encoder_attn.q_proj': 1.362720012664795,\n", "   'encoder_attn.v_proj': 2.468533992767334,\n", "   'fc1': 14.860633850097656,\n", "   'fc2': 0.06362618505954742,\n", "   'self_attn.k_proj': 2.634397506713867,\n", "   'self_attn.out_proj': 0.016836879774928093,\n", "   'self_attn.q_proj': 2.5785069465637207,\n", "   'self_attn.v_proj': 1.0585033893585205},\n", "  {'encoder_attn.k_proj': 4.239994049072266,\n", "   'encoder_attn.out_proj': 0.013666071929037571,\n", "   'encoder_attn.q_proj': 1.4681870937347412,\n", "   'encoder_attn.v_proj': 2.507580280303955,\n", "   'fc1': 14.323543548583984,\n", "   'fc2': 0.06578822433948517,\n", "   'self_attn.k_proj': 2.119868516921997,\n", "   'self_attn.out_proj': 0.011464769020676613,\n", "   'self_attn.q_proj': 2.2465381622314453,\n", "   'self_attn.v_proj': 0.9786300659179688},\n", "  {'encoder_attn.k_proj': 4.5945868492126465,\n", "   'encoder_attn.out_proj': 0.009553573094308376,\n", "   'encoder_attn.q_proj': 2.065673589706421,\n", "   'encoder_attn.v_proj': 2.0542359352111816,\n", "   'fc1': 12.624933242797852,\n", "   'fc2': 0.06195750832557678,\n", "   'self_attn.k_proj': 2.64084529876709,\n", "   'self_attn.out_proj': 0.01331730280071497,\n", "   'self_attn.q_proj': 2.703571319580078,\n", "   'self_attn.v_proj': 1.161910057067871},\n", "  {'encoder_attn.k_proj': 4.3531341552734375,\n", "   'encoder_attn.out_proj': 0.04303068295121193,\n", "   'encoder_attn.q_proj': 2.277655601501465,\n", "   'encoder_attn.v_proj': 2.2393295764923096,\n", "   'fc1': 12.940404891967773,\n", "   'fc2': 0.08074521273374557,\n", "   'self_attn.k_proj': 2.5384888648986816,\n", "   'self_attn.out_proj': 0.011354773305356503,\n", "   'self_attn.q_proj': 2.5611157417297363,\n", "   'self_attn.v_proj': 1.0971941947937012},\n", "  {'encoder_attn.k_proj': 4.542287349700928,\n", "   'encoder_attn.out_proj': 0.029238536953926086,\n", "   'encoder_attn.q_proj': 2.3010153770446777,\n", "   'encoder_attn.v_proj': 2.165403127670288,\n", "   'fc1': 13.617101669311523,\n", "   'fc2': 0.0807381197810173,\n", "   'self_attn.k_proj': 2.7448294162750244,\n", "   'self_attn.out_proj': 0.014555606059730053,\n", "   'self_attn.q_proj': 2.8725650310516357,\n", "   'self_attn.v_proj': 1.1829628944396973},\n", "  {'encoder_attn.k_proj': 4.828767776489258,\n", "   'encoder_attn.out_proj': 0.016674423590302467,\n", "   'encoder_attn.q_proj': 2.1711063385009766,\n", "   'encoder_attn.v_proj': 1.870744228363037,\n", "   'fc1': 13.33492660522461,\n", "   'fc2': 0.07724776118993759,\n", "   'self_attn.k_proj': 3.0165910720825195,\n", "   'self_attn.out_proj': 0.016247933730483055,\n", "   'self_attn.q_proj': 3.1466989517211914,\n", "   'self_attn.v_proj': 1.2117687463760376},\n", "  {'encoder_attn.k_proj': 4.979896068572998,\n", "   'encoder_attn.out_proj': 0.05287356302142143,\n", "   'encoder_attn.q_proj': 2.441960573196411,\n", "   'encoder_attn.v_proj': 2.132169723510742,\n", "   'fc1': 13.768717765808105,\n", "   'fc2': 0.09731274843215942,\n", "   'self_attn.k_proj': 4.235370635986328,\n", "   'self_attn.out_proj': 0.025784075260162354,\n", "   'self_attn.q_proj': 4.358359336853027,\n", "   'self_attn.v_proj': 1.7151672840118408},\n", "  {'encoder_attn.k_proj': 5.030063152313232,\n", "   'encoder_attn.out_proj': 0.03977636992931366,\n", "   'encoder_attn.q_proj': 1.7394684553146362,\n", "   'encoder_attn.v_proj': 1.9284363985061646,\n", "   'fc1': 13.783952713012695,\n", "   'fc2': 0.11163279414176941,\n", "   'self_attn.k_proj': 2.724553108215332,\n", "   'self_attn.out_proj': 0.016170380637049675,\n", "   'self_attn.q_proj': 2.71795916557312,\n", "   'self_attn.v_proj': 1.0455682277679443},\n", "  {'encoder_attn.k_proj': 5.2274627685546875,\n", "   'encoder_attn.out_proj': 0.10354414582252502,\n", "   'encoder_attn.q_proj': 1.9966859817504883,\n", "   'encoder_attn.v_proj': 2.363738536834717,\n", "   'fc1': 15.345691680908203,\n", "   'fc2': 0.14040592312812805,\n", "   'self_attn.k_proj': 3.552276134490967,\n", "   'self_attn.out_proj': 0.024323344230651855,\n", "   'self_attn.q_proj': 3.327355146408081,\n", "   'self_attn.v_proj': 1.3341729640960693},\n", "  {'encoder_attn.k_proj': 5.895442962646484,\n", "   'encoder_attn.out_proj': 0.16168412566184998,\n", "   'encoder_attn.q_proj': 2.2368013858795166,\n", "   'encoder_attn.v_proj': 2.128653049468994,\n", "   'fc1': 19.11541748046875,\n", "   'fc2': 0.16132673621177673,\n", "   'self_attn.k_proj': 3.2103195190429688,\n", "   'self_attn.out_proj': 0.014488482847809792,\n", "   'self_attn.q_proj': 2.6564078330993652,\n", "   'self_attn.v_proj': 1.1326757669448853},\n", "  {'encoder_attn.k_proj': 6.11598014831543,\n", "   'encoder_attn.out_proj': 0.07623652368783951,\n", "   'encoder_attn.q_proj': 1.8586406707763672,\n", "   'encoder_attn.v_proj': 2.461679458618164,\n", "   'fc1': 22.828475952148438,\n", "   'fc2': 0.2076989859342575,\n", "   'self_attn.k_proj': 3.5663294792175293,\n", "   'self_attn.out_proj': 0.01465196069329977,\n", "   'self_attn.q_proj': 2.524531602859497,\n", "   'self_attn.v_proj': 1.2149125337600708},\n", "  {'encoder_attn.k_proj': 6.358344078063965,\n", "   'encoder_attn.out_proj': 0.09254328161478043,\n", "   'encoder_attn.q_proj': 1.6992981433868408,\n", "   'encoder_attn.v_proj': 2.6291022300720215,\n", "   'fc1': 24.739168167114258,\n", "   'fc2': 0.2712007761001587,\n", "   'self_attn.k_proj': 7.002498626708984,\n", "   'self_attn.out_proj': 0.028589623048901558,\n", "   'self_attn.q_proj': 5.079218864440918,\n", "   'self_attn.v_proj': 2.1596970558166504},\n", "  {'encoder_attn.k_proj': 7.428183555603027,\n", "   'encoder_attn.out_proj': 0.05549322068691254,\n", "   'encoder_attn.q_proj': 0.5551271438598633,\n", "   'encoder_attn.v_proj': 3.176450729370117,\n", "   'fc1': 14.37490177154541,\n", "   'fc2': 0.20393197238445282,\n", "   'self_attn.k_proj': 2.1416611671447754,\n", "   'self_attn.out_proj': 0.003167876973748207,\n", "   'self_attn.q_proj': 1.3915114402770996,\n", "   'self_attn.v_proj': 0.6522947549819946}],\n", " 'encoder': [{'fc1': 0.8328455090522766,\n", "   'fc2': 0.004763775505125523,\n", "   'self_attn.k_proj': 0.04299842193722725,\n", "   'self_attn.out_proj': 0.005140198394656181,\n", "   'self_attn.q_proj': 0.13798704743385315,\n", "   'self_attn.v_proj': 0.020348835736513138},\n", "  {'fc1': 0.48540908098220825,\n", "   'fc2': 0.001982554793357849,\n", "   'self_attn.k_proj': 0.15190601348876953,\n", "   'self_attn.out_proj': 0.0026349571999162436,\n", "   'self_attn.q_proj': 0.10911893844604492,\n", "   'self_attn.v_proj': 0.04079374670982361},\n", "  {'fc1': 0.749148428440094,\n", "   'fc2': 0.0007525758119300008,\n", "   'self_attn.k_proj': 0.10404545068740845,\n", "   'self_attn.out_proj': 0.0034286021254956722,\n", "   'self_attn.q_proj': 0.10188504308462143,\n", "   'self_attn.v_proj': 0.053666964173316956},\n", "  {'fc1': 0.7704802751541138,\n", "   'fc2': 0.00173552508931607,\n", "   'self_attn.k_proj': 0.13655005395412445,\n", "   'self_attn.out_proj': 0.0017069161403924227,\n", "   'self_attn.q_proj': 0.11124570667743683,\n", "   'self_attn.v_proj': 0.058697499334812164},\n", "  {'fc1': 0.6973592042922974,\n", "   'fc2': 0.0005532820359803736,\n", "   'self_attn.k_proj': 0.15413028001785278,\n", "   'self_attn.out_proj': 0.0017293801065534353,\n", "   'self_attn.q_proj': 0.11773104965686798,\n", "   'self_attn.v_proj': 0.06841757893562317},\n", "  {'fc1': 0.8629553318023682,\n", "   'fc2': 0.0005312019493430853,\n", "   'self_attn.k_proj': 0.12941068410873413,\n", "   'self_attn.out_proj': 0.003377160057425499,\n", "   'self_attn.q_proj': 0.08875497430562973,\n", "   'self_attn.v_proj': 0.07783172279596329},\n", "  {'fc1': 0.998923122882843,\n", "   'fc2': 0.0005259431200101972,\n", "   'self_attn.k_proj': 0.2935291826725006,\n", "   'self_attn.out_proj': 0.002779396018013358,\n", "   'self_attn.q_proj': 0.2042812705039978,\n", "   'self_attn.v_proj': 0.1427101194858551},\n", "  {'fc1': 0.963005781173706,\n", "   'fc2': 0.00046177857439033687,\n", "   'self_attn.k_proj': 0.3478882908821106,\n", "   'self_attn.out_proj': 0.003308512270450592,\n", "   'self_attn.q_proj': 0.24999983608722687,\n", "   'self_attn.v_proj': 0.1430625319480896},\n", "  {'fc1': 1.3924992084503174,\n", "   'fc2': 0.0005853618495166302,\n", "   'self_attn.k_proj': 0.4035881757736206,\n", "   'self_attn.out_proj': 0.006046154536306858,\n", "   'self_attn.q_proj': 0.3074493408203125,\n", "   'self_attn.v_proj': 0.17925935983657837},\n", "  {'fc1': 1.7101538181304932,\n", "   'fc2': 0.0005166494520381093,\n", "   'self_attn.k_proj': 0.38726499676704407,\n", "   'self_attn.out_proj': 0.0037417374551296234,\n", "   'self_attn.q_proj': 0.2728646397590637,\n", "   'self_attn.v_proj': 0.19768857955932617},\n", "  {'fc1': 2.077885627746582,\n", "   'fc2': 0.0005735599552281201,\n", "   'self_attn.k_proj': 0.31027325987815857,\n", "   'self_attn.out_proj': 0.006215176545083523,\n", "   'self_attn.q_proj': 0.24986155331134796,\n", "   'self_attn.v_proj': 0.22209692001342773},\n", "  {'fc1': 2.0534448623657227,\n", "   'fc2': 0.0007610563770867884,\n", "   'self_attn.k_proj': 0.41368716955184937,\n", "   'self_attn.out_proj': 0.00553455576300621,\n", "   'self_attn.q_proj': 0.27266445755958557,\n", "   'self_attn.v_proj': 0.18975183367729187},\n", "  {'fc1': 2.046889305114746,\n", "   'fc2': 0.0011601037112995982,\n", "   'self_attn.k_proj': 0.3883328437805176,\n", "   'self_attn.out_proj': 0.0048789046704769135,\n", "   'self_attn.q_proj': 0.25899598002433777,\n", "   'self_attn.v_proj': 0.1632937341928482},\n", "  {'fc1': 2.1364879608154297,\n", "   'fc2': 0.0017859982326626778,\n", "   'self_attn.k_proj': 0.35523802042007446,\n", "   'self_attn.out_proj': 0.005680084694176912,\n", "   'self_attn.q_proj': 0.2504822015762329,\n", "   'self_attn.v_proj': 0.17298918962478638},\n", "  {'fc1': 2.65448260307312,\n", "   'fc2': 0.003480421844869852,\n", "   'self_attn.k_proj': 0.40209293365478516,\n", "   'self_attn.out_proj': 0.0029716072604060173,\n", "   'self_attn.q_proj': 0.27673661708831787,\n", "   'self_attn.v_proj': 0.15360690653324127},\n", "  {'fc1': 3.0780930519104004,\n", "   'fc2': 0.0022489619441330433,\n", "   'self_attn.k_proj': 0.41230008006095886,\n", "   'self_attn.out_proj': 0.005530660040676594,\n", "   'self_attn.q_proj': 0.320814847946167,\n", "   'self_attn.v_proj': 0.20632465183734894},\n", "  {'fc1': 3.4365978240966797,\n", "   'fc2': 0.004560950677841902,\n", "   'self_attn.k_proj': 0.2973077595233917,\n", "   'self_attn.out_proj': 0.0050605349242687225,\n", "   'self_attn.q_proj': 0.21249836683273315,\n", "   'self_attn.v_proj': 0.1484196037054062},\n", "  {'fc1': 3.892223358154297,\n", "   'fc2': 0.007856322452425957,\n", "   'self_attn.k_proj': 0.4992671608924866,\n", "   'self_attn.out_proj': 0.006062019616365433,\n", "   'self_attn.q_proj': 0.42249464988708496,\n", "   'self_attn.v_proj': 0.23604537546634674},\n", "  {'fc1': 4.415544033050537,\n", "   'fc2': 0.011455047875642776,\n", "   'self_attn.k_proj': 0.5034027099609375,\n", "   'self_attn.out_proj': 0.006828728131949902,\n", "   'self_attn.q_proj': 0.40311896800994873,\n", "   'self_attn.v_proj': 0.24145996570587158},\n", "  {'fc1': 4.531844139099121,\n", "   'fc2': 0.017653020098805428,\n", "   'self_attn.k_proj': 0.4765814244747162,\n", "   'self_attn.out_proj': 0.007263580337166786,\n", "   'self_attn.q_proj': 0.45319420099258423,\n", "   'self_attn.v_proj': 0.22975683212280273},\n", "  {'fc1': 5.5729570388793945,\n", "   'fc2': 0.1270284652709961,\n", "   'self_attn.k_proj': 0.8339928388595581,\n", "   'self_attn.out_proj': 0.01683463528752327,\n", "   'self_attn.q_proj': 0.859074592590332,\n", "   'self_attn.v_proj': 0.3921084702014923},\n", "  {'fc1': 6.115815162658691,\n", "   'fc2': 0.03299104794859886,\n", "   'self_attn.k_proj': 0.8564786911010742,\n", "   'self_attn.out_proj': 0.012361890636384487,\n", "   'self_attn.q_proj': 0.884661853313446,\n", "   'self_attn.v_proj': 0.3871564567089081},\n", "  {'fc1': 6.341050148010254,\n", "   'fc2': 0.03503289818763733,\n", "   'self_attn.k_proj': 1.2777763605117798,\n", "   'self_attn.out_proj': 0.02573145367205143,\n", "   'self_attn.q_proj': 1.3382160663604736,\n", "   'self_attn.v_proj': 0.5438632965087891},\n", "  {'fc1': 7.4755859375,\n", "   'fc2': 0.04547891393303871,\n", "   'self_attn.k_proj': 1.190417766571045,\n", "   'self_attn.out_proj': 0.022257918491959572,\n", "   'self_attn.q_proj': 1.2652406692504883,\n", "   'self_attn.v_proj': 0.5588939189910889},\n", "  {'fc1': 8.063196182250977,\n", "   'fc2': 0.051832690834999084,\n", "   'self_attn.k_proj': 1.7083712816238403,\n", "   'self_attn.out_proj': 0.036583125591278076,\n", "   'self_attn.q_proj': 1.6431326866149902,\n", "   'self_attn.v_proj': 0.7353314161300659},\n", "  {'fc1': 9.291224479675293,\n", "   'fc2': 0.06857585906982422,\n", "   'self_attn.k_proj': 1.7295506000518799,\n", "   'self_attn.out_proj': 0.021124325692653656,\n", "   'self_attn.q_proj': 1.6681630611419678,\n", "   'self_attn.v_proj': 0.7392783164978027},\n", "  {'fc1': 12.652069091796875,\n", "   'fc2': 0.07671772688627243,\n", "   'self_attn.k_proj': 2.161527633666992,\n", "   'self_attn.out_proj': 0.031025227159261703,\n", "   'self_attn.q_proj': 2.0722877979278564,\n", "   'self_attn.v_proj': 0.9113683104515076},\n", "  {'fc1': 14.965509414672852,\n", "   'fc2': 0.09275263547897339,\n", "   'self_attn.k_proj': 1.983025074005127,\n", "   'self_attn.out_proj': 0.015141794458031654,\n", "   'self_attn.q_proj': 1.6669690608978271,\n", "   'self_attn.v_proj': 0.8219031691551208},\n", "  {'fc1': 16.207448959350586,\n", "   'fc2': 0.11383195221424103,\n", "   'self_attn.k_proj': 2.537534713745117,\n", "   'self_attn.out_proj': 0.04750177264213562,\n", "   'self_attn.q_proj': 2.2889325618743896,\n", "   'self_attn.v_proj': 1.156382441520691},\n", "  {'fc1': 15.87651538848877,\n", "   'fc2': 0.1362086832523346,\n", "   'self_attn.k_proj': 3.2510523796081543,\n", "   'self_attn.out_proj': 0.08618956804275513,\n", "   'self_attn.q_proj': 3.300837516784668,\n", "   'self_attn.v_proj': 1.55784273147583},\n", "  {'fc1': 15.870080947875977,\n", "   'fc2': 0.17930160462856293,\n", "   'self_attn.k_proj': 3.3661768436431885,\n", "   'self_attn.out_proj': 0.08848819136619568,\n", "   'self_attn.q_proj': 3.4780325889587402,\n", "   'self_attn.v_proj': 1.720097541809082},\n", "  {'fc1': 14.495707511901855,\n", "   'fc2': 0.19283738732337952,\n", "   'self_attn.k_proj': 3.394711971282959,\n", "   'self_attn.out_proj': 0.14486607909202576,\n", "   'self_attn.q_proj': 3.766724109649658,\n", "   'self_attn.v_proj': 1.7744364738464355}]}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["all_errors[0.5]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sparsity_dict = all_errors[0.5]\n", "for l in sparsity_dict[\"encoder\"]:\n", "    for k in l.keys():\n", "        l[k] = 0.3\n", "for l in sparsity_dict[\"decoder\"]:\n", "    for k in l.keys():\n", "        l[k] = 0.3\n", "\n", "# 0.6\n", "attn_seuil = 4\n", "attn_output_seuil = 0.05\n", "fc1_seuil = 6.2\n", "fc2_seuil = 0.03\n", "\n", "# 0.7\n", "# attn_seuil = 10\n", "# attn_output_seuil = 0.4\n", "# fc1_seuil = 30\n", "# fc2_seuil = 0.1\n", "\n", "# 0.8\n", "# attn_seuil =20\n", "# attn_output_seuil = 0.4\n", "# fc1_seuil = 60\n", "# fc2_seuil = 0.4\n", "\n", "for s in all_errors.keys():\n", "    # print(s)\n", "    if s > 0.9:\n", "        break\n", "    for i, layer_error in enumerate(all_errors[s][\"encoder\"]):\n", "        if layer_error[\"self_attn.k_proj\"] < attn_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"self_attn.k_proj\"] = s\n", "        if layer_error[\"self_attn.q_proj\"] < attn_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"self_attn.q_proj\"] = s\n", "        if layer_error[\"self_attn.v_proj\"] < attn_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"self_attn.v_proj\"] = s\n", "        if layer_error[\"self_attn.out_proj\"] < attn_output_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"self_attn.out_proj\"] = s\n", "        if layer_error[\"fc1\"] < fc1_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"fc1\"] = s\n", "        if layer_error[\"fc2\"] < fc2_seuil:\n", "            sparsity_dict[\"encoder\"][i][\"fc2\"] = s\n", "    for i, layer_error in enumerate(all_errors[s][\"decoder\"]):\n", "        if layer_error[\"self_attn.k_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"self_attn.k_proj\"] = s\n", "        if layer_error[\"self_attn.q_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"self_attn.q_proj\"] = s\n", "        if layer_error[\"self_attn.v_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"self_attn.v_proj\"] = s\n", "        if layer_error[\"self_attn.out_proj\"] < attn_output_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"self_attn.out_proj\"] = s\n", "\n", "        if layer_error[\"encoder_attn.k_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"encoder_attn.k_proj\"] = s\n", "        if layer_error[\"encoder_attn.q_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"encoder_attn.q_proj\"] = s\n", "        if layer_error[\"encoder_attn.v_proj\"] < attn_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"encoder_attn.v_proj\"] = s\n", "        if layer_error[\"encoder_attn.out_proj\"] < attn_output_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"encoder_attn.out_proj\"] = s\n", "\n", "        if layer_error[\"fc1\"] < fc1_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"fc1\"] = s\n", "        if layer_error[\"fc2\"] < fc2_seuil:\n", "            sparsity_dict[\"decoder\"][i][\"fc2\"] = s\n", "\n", "\n", "for k in sparsity_dict[\"encoder\"][0].keys():\n", "    sparsity_dict[\"encoder\"][0][k] = 0.4"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'encoder_attn.k_proj': 0.62,\n", " 'encoder_attn.out_proj': 0.87,\n", " 'encoder_attn.q_proj': 0.84,\n", " 'encoder_attn.v_proj': 0.67,\n", " 'fc1': 0.52,\n", " 'fc2': 0.84,\n", " 'self_attn.k_proj': 0.79,\n", " 'self_attn.out_proj': 0.9,\n", " 'self_attn.q_proj': 0.82,\n", " 'self_attn.v_proj': 0.9}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["sparsity_dict[\"decoder\"][1]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.6090848214285715\n"]}], "source": ["weighted_sparsity = 0\n", "for i, l in enumerate(sparsity_dict[\"encoder\"]):\n", "    weighted_sparsity += (l[\"self_attn.k_proj\"] + l[\"self_attn.q_proj\"] + l[\"self_attn.v_proj\"] + l[\"self_attn.out_proj\"]) + (l[\"fc1\"] + l[\"fc2\"]) * 4\n", "for i, l in enumerate(sparsity_dict[\"decoder\"]):\n", "    weighted_sparsity += (l[\"self_attn.k_proj\"] + l[\"self_attn.q_proj\"] + l[\"self_attn.v_proj\"] + l[\"self_attn.out_proj\"]) + (l[\"fc1\"] + l[\"fc2\"]) * 4\n", "    weighted_sparsity += (l[\"encoder_attn.k_proj\"] + l[\"encoder_attn.q_proj\"] + l[\"encoder_attn.v_proj\"] + l[\"encoder_attn.out_proj\"])\n", "sparsity = weighted_sparsity / (32 * 12 + 32 * 16)\n", "print(sparsity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('/exp/tianteng.gu/projects/sparsegpt-master/mix_config/mix_0.6_new.yaml', 'w') as file:\n", "    yaml.dump(sparsity_dict, file, default_flow_style=False)"]}], "metadata": {"kernelspec": {"display_name": "sparse", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}