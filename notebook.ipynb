{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datautils_whisper import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/load.py:2547: FutureWarning: 'use_auth_token' was deprecated in favor of 'token' in version 2.14.0 and will be removed in 3.0.0.\n", "You can remove this warning by passing 'token=<use_auth_token>' instead.\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/load.py:1486: FutureWarning: The repository for mozilla-foundation/common_voice_5_1 contains custom code which must be executed to correctly load the dataset. You can inspect the repository content at https://hf.co/datasets/mozilla-foundation/common_voice_5_1\n", "You can avoid this message in future by passing the argument `trust_remote_code=True`.\n", "Passing `trust_remote_code=True` will be mandatory to load this dataset from the next major release of `datasets`.\n", "  warnings.warn(\n"]}], "source": ["# dataset = load_dataset(\"LIUM/tedlium\", \"release3\", split=\"test\", cache_dir=\"/exp/tianteng.gu/huggingface/cache\")\n", "\n", "dataset = load_dataset(\"mozilla-foundation/common_voice_5_1\", \"en\", split=\"test\", use_auth_token=True, cache_dir=\"/exp/tianteng.gu/huggingface/cache\")\n", "# dataset = load_dataset(\"/exp/tianteng.gu/huggingface/dataset/openslr/librispeech_asr\", \"other\", split=\"train.500\", cache_dir=\"/exp/tianteng.gu/huggingface/cache\")\n", "dataset = dataset.select([0, 2, 4, 6, 8, 20, 70, 278 ,23, 2355, 5, 633])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from transformers import AutoProcessor\n", "processor = AutoProcessor.from_pretrained(\"openai/whisper-large-v3\")\n", "\n", "whisper_norm = processor.tokenizer._normalize\n", "\n", "def get_text(sample):\n", "    if \"text\" in sample:\n", "        return sample[\"text\"]\n", "    elif \"sentence\" in sample:\n", "        return sample[\"sentence\"]\n", "    elif \"normalized_text\" in sample:\n", "        return sample[\"normalized_text\"]\n", "    elif \"transcript\" in sample:\n", "        return sample[\"transcript\"]\n", "    else:\n", "        raise ValueError(f\"Sample: {sample.keys()} has no transcript.\")\n", "\n", "def normalise(batch):\n", "    batch[\"norm_text\"] = whisper_norm(get_text(batch))\n", "    return batch\n", "\n", "filter_sequences = [\"ignore time segment in scoring\", \"\"]\n", "\n", "def is_target_text_in_range(ref):\n", "    ref = ref.strip()\n", "    return ref not in filter_sequences\n", "\n", "dataset = dataset.map(normalise)\n", "dataset = dataset.filter(is_target_text_in_range, input_columns=[\"norm_text\"])\n", "dataset = dataset.cast_column(\"audio\", Audio(sampling_rate=16000))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["import torch\n", "def get_whisper(model_id):\n", "    import torch\n", "    def skip(*args, **kwargs):\n", "        pass\n", "    torch.nn.init.kaiming_uniform_ = skip\n", "    torch.nn.init.uniform_ = skip\n", "    torch.nn.init.normal_ = skip\n", "    from transformers import AutoModelForSpeechSeq2Seq\n", "    torch_dtype = torch.float16\n", "    model = AutoModelForSpeechSeq2Seq.from_pretrained(\n", "        model_id, torch_dtype=torch_dtype, low_cpu_mem_usage=True, use_safetensors=True\n", "    )\n", "    return model\n", "model = get_whisper(\"/exp/tianteng.gu/projects/sparsegpt-master/exp/mix_0.5_other\")\n", "device = torch.device(\"cuda:0\")\n", "model.to(device)\n", "from transformers import AutoProcessor\n", "processor = AutoProcessor.from_pretrained(\"openai/whisper-large-v3\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["807468198\n"]}], "source": ["import torch.nn as nn\n", "def get_num_parameters(model: nn.<PERSON><PERSON>, count_nonzero_only=False) -> int:\n", "    \"\"\"\n", "    calculate the total number of parameters of model\n", "    :param count_nonzero_only: only count nonzero weights\n", "    \"\"\"\n", "    num_counted_elements = 0\n", "    for param in model.parameters():\n", "        if count_nonzero_only:\n", "            num_counted_elements += param.count_nonzero()\n", "        else:\n", "            num_counted_elements += param.numel()\n", "    num_counted_elements = int(num_counted_elements)\n", "    return num_counted_elements\n", "\n", "print(get_num_parameters(model, count_nonzero_only=True))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["You have passed task=transcribe, but also have set `forced_decoder_ids` to [[1, None], [2, 50360]] which creates a conflict. `forced_decoder_ids` will be ignored in favor of task=transcribe.\n", "The attention mask is not set and cannot be inferred from input because pad token is same as eos token.As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([2, 128, 3000])\n", "tensor([[50258, 50259, 50360, 50364,   467,   390,   264,   565,   295,   786,\n", "           293,   439,   295, 12838, 17400,  1830,   264,  4266,    13, 50257],\n", "        [50258, 50259, 50360, 50364, 45741,  1907,   796,   300,   750,   390,\n", "           456,   281,   536,   720,  3708,    13, 50257, 50257, 50257, 50257]],\n", "       device='cuda:0')\n"]}], "source": ["batch = dataset[:2]\n", "input_features = processor([batch[\"audio\"][i][\"array\"] for i in range(len(batch[\"audio\"]))], sampling_rate=16000, return_tensors=\"pt\").input_features\n", "print(input_features.shape)\n", "input_features = input_features.to(model.dtype)\n", "predicted_ids = model.generate(input_features.to(\"cuda\"), language=\"en\", task=\"transcribe\")\n", "print(predicted_ids)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<|startoftranscript|><|en|><|transcribe|><|notimestamps|> It was the time of day and all of Spain slept during the summer.<|endoftext|>', '<|startoftranscript|><|en|><|transcribe|><|notimestamps|> <PERSON> told him that she was there to see her brother.<|endoftext|><|endoftext|><|endoftext|><|endoftext|>']\n"]}], "source": ["text = processor.tokenizer.batch_decode(predicted_ids)\n", "print(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def map_to_pred(batch):\n", "    input_features = processor([batch[\"audio\"][i][\"array\"] for i in range(len(batch[\"audio\"]))], sampling_rate=16000, return_tensors=\"pt\").input_features\n", "    input_features = input_features.to(model.dtype)\n", "    batch[\"reference\"] = [processor.tokenizer._normalize(get_text(batch)[i]) for i in range(len(batch[\"audio\"]))]\n", "\n", "    with torch.no_grad():\n", "        predicted_ids = model.generate(input_features.to(\"cuda\"), language=\"en\", task=\"transcribe\")\n", "        transcription = processor.batch_decode(predicted_ids)\n", "        batch[\"prediction\"] = [processor.tokenizer._normalize(transcription[i]) for i in range(len(batch[\"audio\"]))]\n", "    return batch"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "18d850950b8847c894cb6a6dd8823d3f", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/16028 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "OutOfMemoryError", "evalue": "CUDA out of memory. Tried to allocate 118.00 MiB. GPU ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mOutOfMemoryError\u001b[0m                          <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mdataset\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmap\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmap_to_pred\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatched\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m32\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/arrow_dataset.py:602\u001b[0m, in \u001b[0;36mtransmit_tasks.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    600\u001b[0m     \u001b[38;5;28mself\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataset\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mself\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    601\u001b[0m \u001b[38;5;66;03m# apply actual function\u001b[39;00m\n\u001b[0;32m--> 602\u001b[0m out: Union[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataset\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDatasetDict\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    603\u001b[0m datasets: List[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataset\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(out\u001b[38;5;241m.\u001b[39mvalues()) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(out, \u001b[38;5;28mdict\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m [out]\n\u001b[1;32m    604\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m dataset \u001b[38;5;129;01min\u001b[39;00m datasets:\n\u001b[1;32m    605\u001b[0m     \u001b[38;5;66;03m# Remove task templates if a column mapping of the template is no longer valid\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/arrow_dataset.py:567\u001b[0m, in \u001b[0;36mtransmit_format.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    560\u001b[0m self_format \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtype\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_type,\n\u001b[1;32m    562\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mformat_kwargs\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_kwargs,\n\u001b[1;32m    563\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcolumns\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_columns,\n\u001b[1;32m    564\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moutput_all_columns\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_output_all_columns,\n\u001b[1;32m    565\u001b[0m }\n\u001b[1;32m    566\u001b[0m \u001b[38;5;66;03m# apply actual function\u001b[39;00m\n\u001b[0;32m--> 567\u001b[0m out: Union[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataset\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDatasetDict\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    568\u001b[0m datasets: List[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDataset\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(out\u001b[38;5;241m.\u001b[39mvalues()) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(out, \u001b[38;5;28mdict\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m [out]\n\u001b[1;32m    569\u001b[0m \u001b[38;5;66;03m# re-apply format to the output\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/arrow_dataset.py:3156\u001b[0m, in \u001b[0;36mDataset.map\u001b[0;34m(self, function, with_indices, with_rank, input_columns, batched, batch_size, drop_last_batch, remove_columns, keep_in_memory, load_from_cache_file, cache_file_name, writer_batch_size, features, disable_nullable, fn_kwargs, num_proc, suffix_template, new_fingerprint, desc)\u001b[0m\n\u001b[1;32m   3150\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m transformed_dataset \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   3151\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m hf_tqdm(\n\u001b[1;32m   3152\u001b[0m         unit\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m examples\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   3153\u001b[0m         total\u001b[38;5;241m=\u001b[39mpbar_total,\n\u001b[1;32m   3154\u001b[0m         desc\u001b[38;5;241m=\u001b[39mdesc \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMap\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   3155\u001b[0m     ) \u001b[38;5;28;01mas\u001b[39;00m pbar:\n\u001b[0;32m-> 3156\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m rank, done, content \u001b[38;5;129;01min\u001b[39;00m Dataset\u001b[38;5;241m.\u001b[39m_map_single(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mdataset_kwargs):\n\u001b[1;32m   3157\u001b[0m             \u001b[38;5;28;01mif\u001b[39;00m done:\n\u001b[1;32m   3158\u001b[0m                 shards_done \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/arrow_dataset.py:3547\u001b[0m, in \u001b[0;36mDataset._map_single\u001b[0;34m(shard, function, with_indices, with_rank, input_columns, batched, batch_size, drop_last_batch, remove_columns, keep_in_memory, cache_file_name, writer_batch_size, features, disable_nullable, fn_kwargs, new_fingerprint, rank, offset)\u001b[0m\n\u001b[1;32m   3543\u001b[0m indices \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\n\u001b[1;32m   3544\u001b[0m     \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;241m*\u001b[39m(\u001b[38;5;28mslice\u001b[39m(i, i \u001b[38;5;241m+\u001b[39m batch_size)\u001b[38;5;241m.\u001b[39mindices(shard\u001b[38;5;241m.\u001b[39mnum_rows)))\n\u001b[1;32m   3545\u001b[0m )  \u001b[38;5;66;03m# Something simpler?\u001b[39;00m\n\u001b[1;32m   3546\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3547\u001b[0m     batch \u001b[38;5;241m=\u001b[39m \u001b[43mapply_function_on_filtered_inputs\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   3548\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbatch\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3549\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindices\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3550\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcheck_same_num_examples\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mshard\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlist_indexes\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m>\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3551\u001b[0m \u001b[43m        \u001b[49m\u001b[43moffset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffset\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   3552\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3553\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m NumExamplesMismatchError:\n\u001b[1;32m   3554\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m DatasetTransformationNotAllowedError(\n\u001b[1;32m   3555\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUsing `.map` in batched mode on a dataset with attached indexes is allowed only if it doesn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt create or remove existing examples. You can first run `.drop_index() to remove your index and then re-add it.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   3556\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/datasets/arrow_dataset.py:3416\u001b[0m, in \u001b[0;36mDataset._map_single.<locals>.apply_function_on_filtered_inputs\u001b[0;34m(pa_inputs, indices, check_same_num_examples, offset)\u001b[0m\n\u001b[1;32m   3414\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m with_rank:\n\u001b[1;32m   3415\u001b[0m     additional_args \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m (rank,)\n\u001b[0;32m-> 3416\u001b[0m processed_inputs \u001b[38;5;241m=\u001b[39m \u001b[43mfunction\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfn_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43madditional_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfn_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3417\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(processed_inputs, LazyDict):\n\u001b[1;32m   3418\u001b[0m     processed_inputs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m   3419\u001b[0m         k: v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m processed_inputs\u001b[38;5;241m.\u001b[39mdata\u001b[38;5;241m.\u001b[39mitems() \u001b[38;5;28;01mif\u001b[39;00m k \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m processed_inputs\u001b[38;5;241m.\u001b[39mkeys_to_format\n\u001b[1;32m   3420\u001b[0m     }\n", "Cell \u001b[0;32mIn[7], line 7\u001b[0m, in \u001b[0;36mmap_to_pred\u001b[0;34m(batch)\u001b[0m\n\u001b[1;32m      4\u001b[0m batch[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreference\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m [processor\u001b[38;5;241m.\u001b[39m<PERSON><PERSON><PERSON>\u001b[38;5;241m.\u001b[39m_normalize(get_text(batch)[i]) \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(batch[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m]))]\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mno_grad():\n\u001b[0;32m----> 7\u001b[0m     predicted_ids \u001b[38;5;241m=\u001b[39m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[43minput_features\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcuda\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlanguage\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43men\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtranscribe\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      8\u001b[0m     transcription \u001b[38;5;241m=\u001b[39m processor\u001b[38;5;241m.\u001b[39mbatch_decode(predicted_ids)\n\u001b[1;32m      9\u001b[0m     batch[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprediction\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m [processor\u001b[38;5;241m.\u001b[39mtokenizer\u001b[38;5;241m.\u001b[39m_normalize(transcription[i]) \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(batch[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m]))]\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/generation_whisper.py:587\u001b[0m, in \u001b[0;36mWhisperGenerationMixin.generate\u001b[0;34m(self, input_features, generation_config, logits_processor, stopping_criteria, prefix_allowed_tokens_fn, synced_gpus, return_timestamps, task, language, is_multilingual, prompt_ids, prompt_condition_type, condition_on_prev_tokens, temperature, compression_ratio_threshold, logprob_threshold, no_speech_threshold, num_segment_frames, attention_mask, time_precision, return_token_timestamps, return_segments, return_dict_in_generate, **kwargs)\u001b[0m\n\u001b[1;32m    577\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m max_new_tokens \u001b[38;5;241m+\u001b[39m decoder_input_ids\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m] \u001b[38;5;241m>\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mmax_target_positions:\n\u001b[1;32m    578\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    579\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe length of `decoder_input_ids` equal `prompt_ids` plus special start tokens is \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdecoder_input_ids\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, and the `max_new_tokens` \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    580\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mis \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmax_new_tokens\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. Thus, the combined length of \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    584\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mso that their combined length is less than \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mmax_target_positions\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    585\u001b[0m     )\n\u001b[0;32m--> 587\u001b[0m outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    588\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    589\u001b[0m \u001b[43m    \u001b[49m\u001b[43mgeneration_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgeneration_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    590\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlogits_processor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlogits_processor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstopping_criteria\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstopping_criteria\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    592\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprefix_allowed_tokens_fn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprefix_allowed_tokens_fn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    593\u001b[0m \u001b[43m    \u001b[49m\u001b[43msynced_gpus\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msynced_gpus\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    594\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_input_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_input_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    595\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    596\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    598\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m generation_config\u001b[38;5;241m.\u001b[39mreturn_token_timestamps \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(generation_config, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124malignment_heads\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m    599\u001b[0m     outputs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtoken_timestamps\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_extract_token_timestamps(\n\u001b[1;32m    600\u001b[0m         outputs, generation_config\u001b[38;5;241m.\u001b[39malignment_heads, num_frames\u001b[38;5;241m=\u001b[39mgeneration_config\u001b[38;5;241m.\u001b[39mnum_frames\n\u001b[1;32m    601\u001b[0m     )\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/utils/_contextlib.py:115\u001b[0m, in \u001b[0;36mcontext_decorator.<locals>.decorate_context\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;129m@functools\u001b[39m\u001b[38;5;241m.\u001b[39mwraps(func)\n\u001b[1;32m    113\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdecorate_context\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    114\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m ctx_factory():\n\u001b[0;32m--> 115\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/generation/utils.py:1914\u001b[0m, in \u001b[0;36mGenerationMixin.generate\u001b[0;34m(self, inputs, generation_config, logits_processor, stopping_criteria, prefix_allowed_tokens_fn, synced_gpus, assistant_model, streamer, negative_prompt_ids, negative_prompt_attention_mask, **kwargs)\u001b[0m\n\u001b[1;32m   1906\u001b[0m     input_ids, model_kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_expand_inputs_for_generation(\n\u001b[1;32m   1907\u001b[0m         input_ids\u001b[38;5;241m=\u001b[39minput_ids,\n\u001b[1;32m   1908\u001b[0m         expand_size\u001b[38;5;241m=\u001b[39mgeneration_config\u001b[38;5;241m.\u001b[39mnum_return_sequences,\n\u001b[1;32m   1909\u001b[0m         is_encoder_decoder\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mis_encoder_decoder,\n\u001b[1;32m   1910\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mmodel_kwargs,\n\u001b[1;32m   1911\u001b[0m     )\n\u001b[1;32m   1913\u001b[0m     \u001b[38;5;66;03m# 13. run sample (it degenerates to greedy search when `generation_config.do_sample=False`)\u001b[39;00m\n\u001b[0;32m-> 1914\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sample\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1915\u001b[0m \u001b[43m        \u001b[49m\u001b[43minput_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1916\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlogits_processor\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprepared_logits_processor\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1917\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlogits_warper\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprepared_logits_warper\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1918\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstopping_criteria\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprepared_stopping_criteria\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1919\u001b[0m \u001b[43m        \u001b[49m\u001b[43mgeneration_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgeneration_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1920\u001b[0m \u001b[43m        \u001b[49m\u001b[43msynced_gpus\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msynced_gpus\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1921\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstreamer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstreamer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1922\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mmodel_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1923\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1925\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m generation_mode \u001b[38;5;129;01min\u001b[39;00m (GenerationMode\u001b[38;5;241m.\u001b[39mBEAM_SAMPLE, GenerationMode\u001b[38;5;241m.\u001b[39mBEAM_SEARCH):\n\u001b[1;32m   1926\u001b[0m     \u001b[38;5;66;03m# 11. prepare logits warper\u001b[39;00m\n\u001b[1;32m   1927\u001b[0m     prepared_logits_warper \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m   1928\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_logits_warper(generation_config, device\u001b[38;5;241m=\u001b[39minput_ids\u001b[38;5;241m.\u001b[39mdevice)\n\u001b[1;32m   1929\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m generation_config\u001b[38;5;241m.\u001b[39mdo_sample\n\u001b[1;32m   1930\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1931\u001b[0m     )\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/generation/utils.py:2651\u001b[0m, in \u001b[0;36mGenerationMixin._sample\u001b[0;34m(self, input_ids, logits_processor, stopping_criteria, generation_config, synced_gpus, streamer, logits_warper, **model_kwargs)\u001b[0m\n\u001b[1;32m   2648\u001b[0m model_inputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprepare_inputs_for_generation(input_ids, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mmodel_kwargs)\n\u001b[1;32m   2650\u001b[0m \u001b[38;5;66;03m# forward pass to get next token\u001b[39;00m\n\u001b[0;32m-> 2651\u001b[0m outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2652\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mmodel_inputs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2653\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreturn_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   2654\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_attentions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_attentions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2655\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_hidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_hidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2656\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2658\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m synced_gpus \u001b[38;5;129;01mand\u001b[39;00m this_peer_finished:\n\u001b[1;32m   2659\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m  \u001b[38;5;66;03m# don't waste resources running the code we don't need\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1532\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1530\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1531\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1532\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1541\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1536\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1537\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1538\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1539\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1540\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1541\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1543\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1544\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:1753\u001b[0m, in \u001b[0;36mWhisperForConditionalGeneration.forward\u001b[0;34m(self, input_features, attention_mask, decoder_input_ids, decoder_attention_mask, head_mask, decoder_head_mask, cross_attn_head_mask, encoder_outputs, past_key_values, decoder_inputs_embeds, decoder_position_ids, labels, use_cache, output_attentions, output_hidden_states, return_dict)\u001b[0m\n\u001b[1;32m   1748\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m decoder_input_ids \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m decoder_inputs_embeds \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1749\u001b[0m         decoder_input_ids \u001b[38;5;241m=\u001b[39m shift_tokens_right(\n\u001b[1;32m   1750\u001b[0m             labels, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mpad_token_id, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mdecoder_start_token_id\n\u001b[1;32m   1751\u001b[0m         )\n\u001b[0;32m-> 1753\u001b[0m outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1754\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_features\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1755\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattention_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mattention_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1756\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_input_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_input_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1757\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoder_outputs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoder_outputs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1758\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_attention_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_attention_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1759\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhead_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhead_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1760\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_head_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1761\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1762\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpast_key_values\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpast_key_values\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1763\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_inputs_embeds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_inputs_embeds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1764\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoder_position_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_position_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1765\u001b[0m \u001b[43m    \u001b[49m\u001b[43muse_cache\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_cache\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1766\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_attentions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_attentions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1767\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_hidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_hidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1768\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreturn_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mreturn_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1769\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1770\u001b[0m lm_logits \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mproj_out(outputs[\u001b[38;5;241m0\u001b[39m])\n\u001b[1;32m   1772\u001b[0m loss \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1532\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1530\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1531\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1532\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1541\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1536\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1537\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1538\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1539\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1540\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1541\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1543\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1544\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:1627\u001b[0m, in \u001b[0;36mWhisperModel.forward\u001b[0;34m(self, input_features, attention_mask, decoder_input_ids, decoder_attention_mask, head_mask, decoder_head_mask, cross_attn_head_mask, encoder_outputs, past_key_values, decoder_inputs_embeds, decoder_position_ids, use_cache, output_attentions, output_hidden_states, return_dict)\u001b[0m\n\u001b[1;32m   1620\u001b[0m     encoder_outputs \u001b[38;5;241m=\u001b[39m BaseModelOutput(\n\u001b[1;32m   1621\u001b[0m         last_hidden_state\u001b[38;5;241m=\u001b[39mencoder_outputs[\u001b[38;5;241m0\u001b[39m],\n\u001b[1;32m   1622\u001b[0m         hidden_states\u001b[38;5;241m=\u001b[39mencoder_outputs[\u001b[38;5;241m1\u001b[39m] \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(encoder_outputs) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[1;32m   1623\u001b[0m         attentions\u001b[38;5;241m=\u001b[39mencoder_outputs[\u001b[38;5;241m2\u001b[39m] \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(encoder_outputs) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m2\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1624\u001b[0m     )\n\u001b[1;32m   1626\u001b[0m \u001b[38;5;66;03m# decoder outputs consists of (dec_features, past_key_value, dec_hidden, dec_attn)\u001b[39;00m\n\u001b[0;32m-> 1627\u001b[0m decoder_outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdecoder\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1628\u001b[0m \u001b[43m    \u001b[49m\u001b[43minput_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_input_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1629\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattention_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_attention_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1630\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoder_hidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoder_outputs\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1631\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhead_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_head_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1632\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1633\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpast_key_values\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpast_key_values\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1634\u001b[0m \u001b[43m    \u001b[49m\u001b[43minputs_embeds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_inputs_embeds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1635\u001b[0m \u001b[43m    \u001b[49m\u001b[43mposition_ids\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoder_position_ids\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1636\u001b[0m \u001b[43m    \u001b[49m\u001b[43muse_cache\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_cache\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1637\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_attentions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_attentions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1638\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_hidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_hidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1639\u001b[0m \u001b[43m    \u001b[49m\u001b[43mreturn_dict\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mreturn_dict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1640\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1642\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m return_dict:\n\u001b[1;32m   1643\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m decoder_outputs \u001b[38;5;241m+\u001b[39m encoder_outputs\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1532\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1530\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1531\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1532\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1541\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1536\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1537\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1538\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1539\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1540\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1541\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1543\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1544\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:1443\u001b[0m, in \u001b[0;36mWhisperDecoder.forward\u001b[0;34m(self, input_ids, attention_mask, encoder_hidden_states, head_mask, cross_attn_head_mask, past_key_values, inputs_embeds, position_ids, use_cache, output_attentions, output_hidden_states, return_dict)\u001b[0m\n\u001b[1;32m   1430\u001b[0m     layer_outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_gradient_checkpointing_func(\n\u001b[1;32m   1431\u001b[0m         decoder_layer\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__call__\u001b[39m,\n\u001b[1;32m   1432\u001b[0m         hidden_states,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1440\u001b[0m         use_cache,\n\u001b[1;32m   1441\u001b[0m     )\n\u001b[1;32m   1442\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1443\u001b[0m     layer_outputs \u001b[38;5;241m=\u001b[39m \u001b[43mdecoder_layer\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1444\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1445\u001b[0m \u001b[43m        \u001b[49m\u001b[43mattention_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mattention_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1446\u001b[0m \u001b[43m        \u001b[49m\u001b[43mencoder_hidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoder_hidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1447\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlayer_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhead_mask\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mhead_mask\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1448\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcross_attn_layer_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1449\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[43m[\u001b[49m\u001b[43midx\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mcross_attn_head_mask\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[1;32m   1450\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1451\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpast_key_value\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpast_key_value\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1452\u001b[0m \u001b[43m        \u001b[49m\u001b[43moutput_attentions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_attentions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1453\u001b[0m \u001b[43m        \u001b[49m\u001b[43muse_cache\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_cache\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1454\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1455\u001b[0m hidden_states \u001b[38;5;241m=\u001b[39m layer_outputs[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m   1457\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m use_cache:\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1532\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1530\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1531\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1532\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1541\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1536\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1537\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1538\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1539\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1540\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1541\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1543\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1544\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:888\u001b[0m, in \u001b[0;36mWhisperDecoderLayer.forward\u001b[0;34m(self, hidden_states, attention_mask, encoder_hidden_states, encoder_attention_mask, layer_head_mask, cross_attn_layer_head_mask, past_key_value, output_attentions, use_cache)\u001b[0m\n\u001b[1;32m    886\u001b[0m \u001b[38;5;66;03m# cross_attn cached key/values tuple is at positions 3,4 of present_key_value tuple\u001b[39;00m\n\u001b[1;32m    887\u001b[0m cross_attn_past_key_value \u001b[38;5;241m=\u001b[39m past_key_value[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m2\u001b[39m:] \u001b[38;5;28;01mif\u001b[39;00m past_key_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m--> 888\u001b[0m hidden_states, cross_attn_weights, cross_attn_present_key_value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoder_attn\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    889\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhidden_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    890\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkey_value_states\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoder_hidden_states\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    891\u001b[0m \u001b[43m    \u001b[49m\u001b[43mattention_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoder_attention_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    892\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlayer_head_mask\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcross_attn_layer_head_mask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    893\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpast_key_value\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcross_attn_past_key_value\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    894\u001b[0m \u001b[43m    \u001b[49m\u001b[43moutput_attentions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moutput_attentions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    895\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    896\u001b[0m hidden_states \u001b[38;5;241m=\u001b[39m nn\u001b[38;5;241m.\u001b[39mfunctional\u001b[38;5;241m.\u001b[39mdropout(hidden_states, p\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdropout, training\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtraining)\n\u001b[1;32m    897\u001b[0m hidden_states \u001b[38;5;241m=\u001b[39m residual \u001b[38;5;241m+\u001b[39m hidden_states\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1532\u001b[0m, in \u001b[0;36mModule._wrapped_call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1530\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compiled_call_impl(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[1;32m   1531\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1532\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/torch/nn/modules/module.py:1541\u001b[0m, in \u001b[0;36mModule._call_impl\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m   1536\u001b[0m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[1;32m   1537\u001b[0m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[1;32m   1538\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_forward_pre_hooks\n\u001b[1;32m   1539\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[1;32m   1540\u001b[0m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[0;32m-> 1541\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1543\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1544\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:665\u001b[0m, in \u001b[0;36mWhisperSdpaAttention.forward\u001b[0;34m(self, hidden_states, key_value_states, past_key_value, attention_mask, layer_head_mask, output_attentions)\u001b[0m\n\u001b[1;32m    662\u001b[0m     value_states \u001b[38;5;241m=\u001b[39m past_key_value[\u001b[38;5;241m1\u001b[39m]\n\u001b[1;32m    663\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m is_cross_attention:\n\u001b[1;32m    664\u001b[0m     \u001b[38;5;66;03m# cross_attentions\u001b[39;00m\n\u001b[0;32m--> 665\u001b[0m     key_states \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_shape\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mk_proj\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey_value_states\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbsz\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    666\u001b[0m     value_states \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_shape(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mv_proj(key_value_states), \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, bsz)\n\u001b[1;32m    667\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m past_key_value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    668\u001b[0m     \u001b[38;5;66;03m# reuse k, v, self_attention\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/sparse/lib/python3.10/site-packages/transformers/models/whisper/modeling_whisper.py:272\u001b[0m, in \u001b[0;36mWhisperAttention._shape\u001b[0;34m(self, tensor, seq_len, bsz)\u001b[0m\n\u001b[1;32m    271\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_shape\u001b[39m(\u001b[38;5;28mself\u001b[39m, tensor: torch\u001b[38;5;241m.\u001b[39mTensor, seq_len: \u001b[38;5;28mint\u001b[39m, bsz: \u001b[38;5;28mint\u001b[39m):\n\u001b[0;32m--> 272\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mtensor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mview\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbsz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseq_len\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnum_heads\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhead_dim\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtranspose\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcontiguous\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mOutOfMemoryError\u001b[0m: CUDA out of memory. Tried to allocate 118.00 MiB. GPU "]}], "source": ["result = dataset.map(map_to_pred, batched=True, batch_size=16)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[-1.7309e+00, -1.9118e+00, -1.4865e+00],\n", "        [ 2.2283e+00,  2.9222e-01, -6.3782e-02],\n", "        [-9.0117e-01, -1.8160e+00,  3.8550e-01],\n", "        [-2.5334e+00, -6.7030e+00,  1.1350e+00],\n", "        [ 1.1993e+00,  2.1864e+00, -1.0655e+00],\n", "        [-1.4311e+00, -2.7788e+00,  1.1778e+00],\n", "        [-6.2364e-04, -9.0071e-01, -1.4061e-01],\n", "        [ 3.1162e+00, -6.5144e-01, -1.2403e+00],\n", "        [-1.8631e+00,  1.5209e+00,  2.3542e+00],\n", "        [ 5.0209e-01, -6.4501e+00, -2.4391e+00]], grad_fn=<MmBackward0>)\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "# 假设我们有输入张量和权重张量\n", "input_tensor = torch.randn(10, 5)  # 假设有10个样本，每个样本有5个特征\n", "weight_tensor = torch.randn(3, 5)  # 权重矩阵，5个输入特征，3个输出特征\n", "\n", "# 创建一个nn.Linear层的实例，并将权重张量作为参数传入\n", "linear_layer = nn.Linear(in_features=5, out_features=3, bias=False)  # 假设我们不使用偏置\n", "with torch.no_grad():  # 不计算梯度，因为我们只是设置权重\n", "    linear_layer.weight = nn.Parameter(weight_tensor)\n", "\n", "# 使用nn.Linear层计算矩阵乘法的结果\n", "output_tensor = linear_layer(input_tensor)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 3])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "a = torch.arange(9, dtype= torch.float) - 4\n", "b = a.reshape((3, 3))\n", "torch.norm(a)\n", "torch.norm(b)\n", "torch.norm(a, float('inf'))\n", "torch.norm(b, float('inf'))\n", "c = torch.tensor([[ 1, 2, 3], [-1, 1, 4]] , dtype=torch.float)\n", "c.shape\n", "# torch.norm(c, dim=0).shape\n", "# torch.norm(c, dim=1).shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["float(c[0,0])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5762499999999999\n"]}], "source": ["import yaml\n", "with open(\"/exp/tianteng.gu/projects/sparsegpt-master/mix_config/other/mix_0.6.yaml\", 'r') as file:\n", "    sparsity_dict = yaml.safe_load(file)\n", "weighted_sparsity = 0\n", "for i, l in enumerate(sparsity_dict[\"encoder\"]):\n", "    weighted_sparsity += (l[\"self_attn.k_proj\"] + l[\"self_attn.q_proj\"] + l[\"self_attn.v_proj\"] + l[\"self_attn.out_proj\"]) + (l[\"fc1\"] + l[\"fc2\"]) * 4\n", "# for i, l in enumerate(sparsity_dict[\"decoder\"]):\n", "#     weighted_sparsity += (l[\"self_attn.k_proj\"] + l[\"self_attn.q_proj\"] + l[\"self_attn.v_proj\"] + l[\"self_attn.out_proj\"]) + (l[\"fc1\"] + l[\"fc2\"]) * 4\n", "#     weighted_sparsity += (l[\"encoder_attn.k_proj\"] + l[\"encoder_attn.q_proj\"] + l[\"encoder_attn.v_proj\"] + l[\"encoder_attn.out_proj\"])\n", "sparsity = weighted_sparsity / (32 * 12) # (32 * 12 + 32 * 16)\n", "print(sparsity)"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "import seaborn as sns\n", "\n", "from matplotlib.font_manager import FontProperties\n", "\n", "cmap = plt.get_cmap('viridis')\n", "default_colors = plt.rcParams['axes.prop_cycle'].by_key()['color']"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "import yaml\n", "\n", "all_errors = {}\n", "\n", "# 指定文件夹路径\n", "folder_path = '/exp/tianteng.gu/projects/sparsegpt-master/whisper_error_libri_other_en'\n", "\n", "# 使用glob模块找到所有YAML文件\n", "yaml_files = glob.glob(os.path.join(folder_path, '*.yaml'))\n", "\n", "# # 遍历所有YAML文件\n", "for file_path in yaml_files:\n", "    with open(file_path, 'r') as file:\n", "        yaml_content = yaml.safe_load(file)\n", "        s = float(os.path.splitext(file_path)[0].split('/')[-1])\n", "        all_errors[s] = yaml_content\n", "\n", "all_errors = dict(sorted(all_errors.items(), key=lambda item: item[0]))"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Reconstruction Error')"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sparsity_list = list(all_errors.keys())\n", "sparsity_list = sparsity_list[:51]\n", "\n", "\n", "layer_ids = [0, 7, 15, 23, 31]\n", "error_dict = {}\n", "for idx in layer_ids:\n", "    error_dict[idx] = []\n", "\n", "for idx in layer_ids:\n", "    for s in sparsity_list:\n", "        # if s > 0.8:\n", "        #     break\n", "        error_dict[idx].append(all_errors[s][\"decoder\"][idx][\"fc2\"])\n", "\n", "sparsity_labels = [s * 100 for s in sparsity_list]\n", "\n", "for i, idx in enumerate(layer_ids):\n", "    plt.plot(sparsity_labels, error_dict[idx], label=\"layer \" + str(idx), linewidth=0.9, c=default_colors[i])\n", "\n", "\n", "threshold = 0.05\n", "target_s_dict = {}\n", "for i, idx in enumerate(layer_ids):\n", "    for s in sparsity_list:\n", "        if all_errors[s][\"decoder\"][idx][\"fc2\"] > threshold:\n", "            break\n", "        target_s_dict[idx] = s\n", "    plt.plot([target_s_dict[idx] * 100, target_s_dict[idx] * 100], [threshold, 0], linestyle='--', linewidth=0.9, c=default_colors[i])\n", "\n", "\n", "plt.axhline(y=threshold, color='k', linestyle='--', linewidth=1.5, label=\"threshold\")\n", "\n", "plt.yscale('log')\n", "\n", "# font = FontProperties(family='Times New Roman', size=12)\n", "# plt.legend(prop=font)\n", "plt.legend()\n", "plt.xlabel(\"Sparsity (%)\")\n", "plt.ylabel(\"Reconstruction Error\")\n", "# plt.grid(True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "sparse", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}