# 生成预测ID序列
print('Generating predicted ids ...')
predicted_ids = []
model = model.to(dev)  # 将完整模型移至GPU设备
batch_size = 16  # 设置批处理大小

# 使用tqdm显示进度条，每批处理16个样本
for i in tqdm(range(0, len(dataloader), 16)):
    # 计算当前批次的起止索引
    start = i
    end = min(len(dataloader), start+batch_size)
    
    # 获取并合并当前批次数据
    batch = dataloader[start:end]
    batch = torch.concatenate(batch, dim=0)  # 在维度0拼接张量
    
    # 生成转录文本的token ID（GPU加速）
    p_i = model.generate(
        batch.to(dev),  # 将数据移至GPU
        language="en",   # 指定英语语言模型
        task="transcribe"  # 设置转录任务模式
    ).cpu()  # 将结果移回CPU内存

    # 清理特殊结束标记（ID 50247）
    for x in list(p_i):
        last_index = len(x) - 1
        # 逆向遍历删除连续结束标记
        while last_index >= 0 and x[last_index] == 50247:
            last_index -= 1
        
        # 保留有效token并保持维度一致性
        cleaned_x = x[:last_index + 1]
        cleaned_x = cleaned_x.unsqueeze(0)  # 添加批次维度
        predicted_ids.append(cleaned_x)

# 将模型移回CPU释放显存
model = model.cpu()