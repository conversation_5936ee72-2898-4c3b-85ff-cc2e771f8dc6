# export CUDA_VISIBLE_DEVICES=1

model=openai/whisper-large-v3
LOGFILE=/exp/tianteng.gu/projects/sparsegpt-main/exp/log-0.5

/home/<USER>/miniconda3/envs/sparse/bin/python /exp/tianteng.gu/projects/sparsegpt-master/whisper_errors.py  \
    --model $model \
    --dataset mls \
    --nsamples 256 \
    --blocksize 256 \
    --encoder_sparsity 0.6 \
    --decoder_sparsity 0.6 \
    --language italian \
    --task transcribe