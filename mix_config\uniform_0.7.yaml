decoder:
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- encoder_attn.k_proj: 0.7
  encoder_attn.out_proj: 0.7
  encoder_attn.q_proj: 0.7
  encoder_attn.v_proj: 0.7
  fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
encoder:
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
- fc1: 0.7
  fc2: 0.7
  self_attn.k_proj: 0.7
  self_attn.out_proj: 0.7
  self_attn.q_proj: 0.7
  self_attn.v_proj: 0.7
