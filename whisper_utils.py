import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import transformers

def encoder_layer_train(model, inps, outs, args):
    pruner = FineGrainedPruner(model)
    model.train()
    for param in model.parameters():
        param.requires_grad = True

    class CustomDataset(Dataset):
        def __init__(self, inps, outs):
            self.inputs = inps
            self.labels = outs

        def __len__(self):
            return len(self.inputs)

        def __getitem__(self, idx):
            input_tensor = self.inputs[idx].squeeze(0).clone().detach()
            label_tensor = self.labels[idx].squeeze(0).clone().detach()
            return input_tensor, label_tensor
    dataset = CustomDataset(inps, outs)
    train_loader = DataLoader(dataset, batch_size=args.bsz, shuffle=True)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr = args.lr)
    for epoch in range(args.epochs):
        running_loss = 0.0
        for inputs, labels in train_loader:
            optimizer.zero_grad()
            outputs = model(inputs, attention_mask=None, layer_head_mask=None)[0]
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            pruner.apply(model)
            running_loss += loss.item()
        print(f"Epoch {epoch+1}, Loss: {running_loss/len(train_loader)}")

    for param in model.parameters():
        param.requires_grad = False
    model.eval()

def decoder_layer_train(model, inps, outs, attention_masks, encoder_hidden_states, args):
    pruner = FineGrainedPruner(model)
    model.train()
    for param in model.parameters():
        param.requires_grad = True

    class CustomDataset(Dataset):
        def __init__(self, inps, outs, att_masks, encoder_hidden_states):
            self.inputs = inps
            self.labels = outs
            self.att_masks = att_masks
            self.encoder_hidden_states = encoder_hidden_states

        def __len__(self):
            return len(self.inputs)

        def __getitem__(self, idx):
            input_tensor = self.inputs[idx].squeeze(0).clone().detach()
            label_tensor = self.labels[idx].squeeze(0).clone().detach()
            hidden_tensor = self.encoder_hidden_states[idx].squeeze(0).clone().detach()
            return input_tensor, label_tensor, hidden_tensor
    dataset = CustomDataset(inps, outs, attention_masks, encoder_hidden_states)
    train_loader = DataLoader(dataset, batch_size=1, shuffle=True)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr = 0.001 * args.lr)
    gradient_accumulation_steps = args.bsz
    for epoch in range(args.epochs):
        running_loss = 0.0
        for i, (inputs, labels, hiddens) in enumerate(train_loader):
            outputs = model(inputs, attention_mask=None, encoder_hidden_states=hiddens)[0]
            loss = criterion(outputs, labels)
            loss.backward()
            running_loss += loss.item()

            if (i + 1) % gradient_accumulation_steps == 0:
                optimizer.step()
                pruner.apply(model)
                optimizer.zero_grad()
        print(f"Epoch {epoch+1}, Loss: {running_loss/len(train_loader)}")

    for param in model.parameters():
        param.requires_grad = False
    model.eval()

class FineGrainedPruner:
    def __init__(self, model):
        self.masks = FineGrainedPruner.get_mask(model)

    @torch.no_grad()
    def apply(self, model):
        for name, param in model.named_parameters():
            if name in self.masks:
                param *= self.masks[name].to(param.device)

    @staticmethod
    @torch.no_grad()
    def get_mask(model):
        masks = dict()
        for name, param in model.named_parameters():
            if param.dim() > 1: # we only prune conv and fc weights
                masks[name] = param != 0
        return masks