decoder:
- encoder_attn.k_proj: 10.9609375
  encoder_attn.out_proj: 288.75
  encoder_attn.q_proj: 3.888671875
  encoder_attn.v_proj: 12.703125
  fc1: 2.86328125
  fc2: 2.732421875
  self_attn.k_proj: 20.890625
  self_attn.out_proj: 2.0
  self_attn.q_proj: 17.03125
  self_attn.v_proj: 6.30859375
- encoder_attn.k_proj: 11.671875
  encoder_attn.out_proj: 109.0625
  encoder_attn.q_proj: 0.32568359375
  encoder_attn.v_proj: 13.1328125
  fc1: 3.296875
  fc2: 0.70458984375
  self_attn.k_proj: 1.1298828125
  self_attn.out_proj: 0.02960205078125
  self_attn.q_proj: 0.873046875
  self_attn.v_proj: 0.19091796875
- encoder_attn.k_proj: 7.23046875
  encoder_attn.out_proj: 22.015625
  encoder_attn.q_proj: 0.491455078125
  encoder_attn.v_proj: 7.5625
  fc1: 5.92578125
  fc2: 0.314697265625
  self_attn.k_proj: 0.328857421875
  self_attn.out_proj: 0.02972412109375
  self_attn.q_proj: 0.480712890625
  self_attn.v_proj: 0.11279296875
- encoder_attn.k_proj: 5.47265625
  encoder_attn.out_proj: 31.15625
  encoder_attn.q_proj: 0.0906982421875
  encoder_attn.v_proj: 5.04296875
  fc1: 3.7734375
  fc2: 0.5673828125
  self_attn.k_proj: 0.1492919921875
  self_attn.out_proj: 0.0192413330078125
  self_attn.q_proj: 0.24072265625
  self_attn.v_proj: 0.10321044921875
- encoder_attn.k_proj: 11.1640625
  encoder_attn.out_proj: 96.875
  encoder_attn.q_proj: 0.5
  encoder_attn.v_proj: 11.359375
  fc1: 2.8046875
  fc2: 0.49853515625
  self_attn.k_proj: 0.20849609375
  self_attn.out_proj: 0.0227203369140625
  self_attn.q_proj: 0.46875
  self_attn.v_proj: 0.1524658203125
- encoder_attn.k_proj: 9.140625
  encoder_attn.out_proj: 148.25
  encoder_attn.q_proj: 0.436767578125
  encoder_attn.v_proj: 9.65625
  fc1: 9.0546875
  fc2: 0.0947265625
  self_attn.k_proj: 0.4365234375
  self_attn.out_proj: 0.1514892578125
  self_attn.q_proj: 0.63671875
  self_attn.v_proj: 0.2445068359375
- encoder_attn.k_proj: 15.859375
  encoder_attn.out_proj: 131.875
  encoder_attn.q_proj: 0.73095703125
  encoder_attn.v_proj: 15.1953125
  fc1: 6.390625
  fc2: 0.12396240234375
  self_attn.k_proj: 0.609375
  self_attn.out_proj: 0.1680908203125
  self_attn.q_proj: 0.6513671875
  self_attn.v_proj: 0.276611328125
- encoder_attn.k_proj: 11.8125
  encoder_attn.out_proj: 177.875
  encoder_attn.q_proj: 1.0908203125
  encoder_attn.v_proj: 13.265625
  fc1: 6.14453125
  fc2: 0.0894775390625
  self_attn.k_proj: 0.7421875
  self_attn.out_proj: 0.248046875
  self_attn.q_proj: 0.73291015625
  self_attn.v_proj: 0.280517578125
- encoder_attn.k_proj: 9.609375
  encoder_attn.out_proj: 97.5
  encoder_attn.q_proj: 2.23046875
  encoder_attn.v_proj: 8.7109375
  fc1: 11.6640625
  fc2: 0.059051513671875
  self_attn.k_proj: 0.92529296875
  self_attn.out_proj: 0.222900390625
  self_attn.q_proj: 0.78466796875
  self_attn.v_proj: 0.332763671875
- encoder_attn.k_proj: 10.046875
  encoder_attn.out_proj: 271.25
  encoder_attn.q_proj: 0.5
  encoder_attn.v_proj: 12.0703125
  fc1: 10.09375
  fc2: 0.09033203125
  self_attn.k_proj: 0.86669921875
  self_attn.out_proj: 0.1761474609375
  self_attn.q_proj: 0.810546875
  self_attn.v_proj: 0.2568359375
- encoder_attn.k_proj: 14.875
  encoder_attn.out_proj: 141.5
  encoder_attn.q_proj: 0.904296875
  encoder_attn.v_proj: 12.28125
  fc1: 10.4375
  fc2: 0.07965087890625
  self_attn.k_proj: 1.427734375
  self_attn.out_proj: 0.2491455078125
  self_attn.q_proj: 1.0341796875
  self_attn.v_proj: 0.389404296875
- encoder_attn.k_proj: 14.5546875
  encoder_attn.out_proj: 318.5
  encoder_attn.q_proj: 0.35546875
  encoder_attn.v_proj: 15.1171875
  fc1: 9.2578125
  fc2: 0.0889892578125
  self_attn.k_proj: 0.96484375
  self_attn.out_proj: 0.1781005859375
  self_attn.q_proj: 0.93994140625
  self_attn.v_proj: 0.367431640625
- encoder_attn.k_proj: 15.9921875
  encoder_attn.out_proj: 110.125
  encoder_attn.q_proj: 0.6611328125
  encoder_attn.v_proj: 17.46875
  fc1: 6.890625
  fc2: 0.125
  self_attn.k_proj: 1.3994140625
  self_attn.out_proj: 0.367919921875
  self_attn.q_proj: 1.2119140625
  self_attn.v_proj: 0.47998046875
- encoder_attn.k_proj: 17.71875
  encoder_attn.out_proj: 369.25
  encoder_attn.q_proj: 0.73291015625
  encoder_attn.v_proj: 17.296875
  fc1: 5.42578125
  fc2: 0.295654296875
  self_attn.k_proj: 1.25
  self_attn.out_proj: 0.31298828125
  self_attn.q_proj: 1.0810546875
  self_attn.v_proj: 0.51416015625
- encoder_attn.k_proj: 17.1875
  encoder_attn.out_proj: 243.375
  encoder_attn.q_proj: 0.37744140625
  encoder_attn.v_proj: 18.5625
  fc1: 5.7421875
  fc2: 0.160400390625
  self_attn.k_proj: 1.146484375
  self_attn.out_proj: 0.2646484375
  self_attn.q_proj: 1.162109375
  self_attn.v_proj: 0.481689453125
- encoder_attn.k_proj: 18.75
  encoder_attn.out_proj: 264.75
  encoder_attn.q_proj: 0.497314453125
  encoder_attn.v_proj: 19.234375
  fc1: 5.56640625
  fc2: 0.2318115234375
  self_attn.k_proj: 1.388671875
  self_attn.out_proj: 0.37255859375
  self_attn.q_proj: 1.2939453125
  self_attn.v_proj: 0.63671875
- encoder_attn.k_proj: 18.765625
  encoder_attn.out_proj: 624.5
  encoder_attn.q_proj: 0.494140625
  encoder_attn.v_proj: 20.75
  fc1: 5.75
  fc2: 0.17822265625
  self_attn.k_proj: 1.232421875
  self_attn.out_proj: 0.252197265625
  self_attn.q_proj: 1.0595703125
  self_attn.v_proj: 0.56787109375
- encoder_attn.k_proj: 18.484375
  encoder_attn.out_proj: 387.0
  encoder_attn.q_proj: 0.705078125
  encoder_attn.v_proj: 20.0
  fc1: 5.25
  fc2: 0.1796875
  self_attn.k_proj: 0.986328125
  self_attn.out_proj: 0.44384765625
  self_attn.q_proj: 0.9873046875
  self_attn.v_proj: 0.578125
- encoder_attn.k_proj: 19.015625
  encoder_attn.out_proj: 644.0
  encoder_attn.q_proj: 0.9404296875
  encoder_attn.v_proj: 21.0
  fc1: 5.2578125
  fc2: 0.15771484375
  self_attn.k_proj: 1.2568359375
  self_attn.out_proj: 0.32275390625
  self_attn.q_proj: 1.15625
  self_attn.v_proj: 0.66357421875
- encoder_attn.k_proj: 20.125
  encoder_attn.out_proj: 505.5
  encoder_attn.q_proj: 0.87890625
  encoder_attn.v_proj: 22.65625
  fc1: 6.140625
  fc2: 0.177734375
  self_attn.k_proj: 0.9169921875
  self_attn.out_proj: 0.179443359375
  self_attn.q_proj: 0.9150390625
  self_attn.v_proj: 0.45556640625
- encoder_attn.k_proj: 20.875
  encoder_attn.out_proj: 686.0
  encoder_attn.q_proj: 0.96337890625
  encoder_attn.v_proj: 22.234375
  fc1: 5.0234375
  fc2: 0.1812744140625
  self_attn.k_proj: 1.443359375
  self_attn.out_proj: 0.2880859375
  self_attn.q_proj: 1.005859375
  self_attn.v_proj: 0.52490234375
- encoder_attn.k_proj: 20.421875
  encoder_attn.out_proj: 452.0
  encoder_attn.q_proj: 0.85693359375
  encoder_attn.v_proj: 21.625
  fc1: 5.43359375
  fc2: 0.161376953125
  self_attn.k_proj: 0.95947265625
  self_attn.out_proj: 0.5
  self_attn.q_proj: 0.9775390625
  self_attn.v_proj: 0.488037109375
- encoder_attn.k_proj: 20.0
  encoder_attn.out_proj: 491.75
  encoder_attn.q_proj: 1.248046875
  encoder_attn.v_proj: 25.484375
  fc1: 4.0
  fc2: 0.1102294921875
  self_attn.k_proj: 1.474609375
  self_attn.out_proj: 0.2222900390625
  self_attn.q_proj: 1.0390625
  self_attn.v_proj: 0.5556640625
- encoder_attn.k_proj: 21.515625
  encoder_attn.out_proj: 576.5
  encoder_attn.q_proj: 0.9384765625
  encoder_attn.v_proj: 23.21875
  fc1: 3.763671875
  fc2: 0.25
  self_attn.k_proj: 1.0
  self_attn.out_proj: 0.24658203125
  self_attn.q_proj: 0.8935546875
  self_attn.v_proj: 0.458251953125
- encoder_attn.k_proj: 20.515625
  encoder_attn.out_proj: 469.5
  encoder_attn.q_proj: 0.849609375
  encoder_attn.v_proj: 22.296875
  fc1: 3.1953125
  fc2: 0.1910400390625
  self_attn.k_proj: 0.95849609375
  self_attn.out_proj: 0.367431640625
  self_attn.q_proj: 0.9052734375
  self_attn.v_proj: 0.38623046875
- encoder_attn.k_proj: 21.625
  encoder_attn.out_proj: 679.5
  encoder_attn.q_proj: 0.79345703125
  encoder_attn.v_proj: 23.671875
  fc1: 4.0
  fc2: 0.2349853515625
  self_attn.k_proj: 1.0
  self_attn.out_proj: 0.2427978515625
  self_attn.q_proj: 0.95751953125
  self_attn.v_proj: 0.5
- encoder_attn.k_proj: 23.40625
  encoder_attn.out_proj: 739.0
  encoder_attn.q_proj: 0.7412109375
  encoder_attn.v_proj: 21.578125
  fc1: 3.9296875
  fc2: 0.1705322265625
  self_attn.k_proj: 1.0625
  self_attn.out_proj: 0.23193359375
  self_attn.q_proj: 0.9755859375
  self_attn.v_proj: 0.496337890625
- encoder_attn.k_proj: 22.609375
  encoder_attn.out_proj: 505.5
  encoder_attn.q_proj: 0.6865234375
  encoder_attn.v_proj: 25.34375
  fc1: 4.45703125
  fc2: 0.1729736328125
  self_attn.k_proj: 1.0
  self_attn.out_proj: 0.25
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.5537109375
- encoder_attn.k_proj: 23.859375
  encoder_attn.out_proj: 563.0
  encoder_attn.q_proj: 0.6533203125
  encoder_attn.v_proj: 24.109375
  fc1: 5.03515625
  fc2: 0.2144775390625
  self_attn.k_proj: 0.90283203125
  self_attn.out_proj: 0.378173828125
  self_attn.q_proj: 0.86083984375
  self_attn.v_proj: 0.46044921875
- encoder_attn.k_proj: 22.859375
  encoder_attn.out_proj: 496.25
  encoder_attn.q_proj: 0.67529296875
  encoder_attn.v_proj: 23.015625
  fc1: 5.390625
  fc2: 0.2158203125
  self_attn.k_proj: 1.0517578125
  self_attn.out_proj: 0.2236328125
  self_attn.q_proj: 1.0
  self_attn.v_proj: 0.489990234375
- encoder_attn.k_proj: 24.265625
  encoder_attn.out_proj: 666.5
  encoder_attn.q_proj: 0.697265625
  encoder_attn.v_proj: 24.5
  fc1: 4.5546875
  fc2: 0.2410888671875
  self_attn.k_proj: 1.34765625
  self_attn.out_proj: 0.323974609375
  self_attn.q_proj: 1.0537109375
  self_attn.v_proj: 0.638671875
- encoder_attn.k_proj: 25.15625
  encoder_attn.out_proj: .inf
  encoder_attn.q_proj: 0.4794921875
  encoder_attn.v_proj: 25.96875
  fc1: 3.826171875
  fc2: 0.305419921875
  self_attn.k_proj: 1.0
  self_attn.out_proj: 0.2239990234375
  self_attn.q_proj: 0.8994140625
  self_attn.v_proj: 0.334716796875
encoder:
- fc1: 4.3203125
  fc2: 0.0977783203125
  self_attn.k_proj: 4.578125
  self_attn.out_proj: 0.1478271484375
  self_attn.q_proj: 4.12109375
  self_attn.v_proj: 3.50390625
- fc1: 1.994140625
  fc2: 0.0193328857421875
  self_attn.k_proj: 0.58642578125
  self_attn.out_proj: 0.0694580078125
  self_attn.q_proj: 0.484375
  self_attn.v_proj: 0.27490234375
- fc1: 2.189453125
  fc2: 0.00801849365234375
  self_attn.k_proj: 0.417724609375
  self_attn.out_proj: 0.07501220703125
  self_attn.q_proj: 0.38818359375
  self_attn.v_proj: 0.266357421875
- fc1: 2.443359375
  fc2: 0.0103302001953125
  self_attn.k_proj: 0.4951171875
  self_attn.out_proj: 0.061981201171875
  self_attn.q_proj: 0.401611328125
  self_attn.v_proj: 0.2880859375
- fc1: 2.0078125
  fc2: 0.006626129150390625
  self_attn.k_proj: 0.49609375
  self_attn.out_proj: 0.05902099609375
  self_attn.q_proj: 0.3955078125
  self_attn.v_proj: 0.293212890625
- fc1: 2.390625
  fc2: 0.0078125
  self_attn.k_proj: 0.46435546875
  self_attn.out_proj: 0.07781982421875
  self_attn.q_proj: 0.347412109375
  self_attn.v_proj: 0.3173828125
- fc1: 2.6484375
  fc2: 0.007762908935546875
  self_attn.k_proj: 0.685546875
  self_attn.out_proj: 0.0709228515625
  self_attn.q_proj: 0.52587890625
  self_attn.v_proj: 0.400390625
- fc1: 2.990234375
  fc2: 0.01099395751953125
  self_attn.k_proj: 0.75
  self_attn.out_proj: 0.0821533203125
  self_attn.q_proj: 0.5869140625
  self_attn.v_proj: 0.41455078125
- fc1: 2.8359375
  fc2: 0.018890380859375
  self_attn.k_proj: 0.7900390625
  self_attn.out_proj: 0.095458984375
  self_attn.q_proj: 0.6171875
  self_attn.v_proj: 0.4384765625
- fc1: 4.12109375
  fc2: 0.0155792236328125
  self_attn.k_proj: 0.76220703125
  self_attn.out_proj: 0.07647705078125
  self_attn.q_proj: 0.57373046875
  self_attn.v_proj: 0.4404296875
- fc1: 3.95703125
  fc2: 0.0184326171875
  self_attn.k_proj: 0.63623046875
  self_attn.out_proj: 0.09649658203125
  self_attn.q_proj: 0.50732421875
  self_attn.v_proj: 0.443603515625
- fc1: 3.7578125
  fc2: 0.017578125
  self_attn.k_proj: 0.759765625
  self_attn.out_proj: 0.08978271484375
  self_attn.q_proj: 0.552734375
  self_attn.v_proj: 0.436279296875
- fc1: 3.76171875
  fc2: 0.02801513671875
  self_attn.k_proj: 0.7568359375
  self_attn.out_proj: 0.0902099609375
  self_attn.q_proj: 0.546875
  self_attn.v_proj: 0.403076171875
- fc1: 3.349609375
  fc2: 0.041839599609375
  self_attn.k_proj: 0.662109375
  self_attn.out_proj: 0.0989990234375
  self_attn.q_proj: 0.501953125
  self_attn.v_proj: 0.384521484375
- fc1: 4.40234375
  fc2: 0.0526123046875
  self_attn.k_proj: 0.76318359375
  self_attn.out_proj: 0.069580078125
  self_attn.q_proj: 0.57177734375
  self_attn.v_proj: 0.385986328125
- fc1: 5.0234375
  fc2: 0.0499267578125
  self_attn.k_proj: 0.6904296875
  self_attn.out_proj: 0.08203125
  self_attn.q_proj: 0.5458984375
  self_attn.v_proj: 0.38623046875
- fc1: 4.84375
  fc2: 0.059844970703125
  self_attn.k_proj: 0.62353515625
  self_attn.out_proj: 0.0999755859375
  self_attn.q_proj: 0.4599609375
  self_attn.v_proj: 0.379638671875
- fc1: .inf
  fc2: 0.07403564453125
  self_attn.k_proj: 0.83837890625
  self_attn.out_proj: 0.100341796875
  self_attn.q_proj: 0.6748046875
  self_attn.v_proj: 0.473876953125
- fc1: .inf
  fc2: 0.0975341796875
  self_attn.k_proj: 0.84228515625
  self_attn.out_proj: 0.1131591796875
  self_attn.q_proj: 0.64404296875
  self_attn.v_proj: 0.47119140625
- fc1: .inf
  fc2: 0.11700439453125
  self_attn.k_proj: 0.81689453125
  self_attn.out_proj: 0.11358642578125
  self_attn.q_proj: 0.70263671875
  self_attn.v_proj: 0.47412109375
- fc1: .inf
  fc2: 0.1683349609375
  self_attn.k_proj: 0.98046875
  self_attn.out_proj: 0.162109375
  self_attn.q_proj: 0.9560546875
  self_attn.v_proj: 0.57373046875
- fc1: .inf
  fc2: 0.1502685546875
  self_attn.k_proj: 1.00390625
  self_attn.out_proj: 0.1400146484375
  self_attn.q_proj: 0.9619140625
  self_attn.v_proj: 0.591796875
- fc1: .inf
  fc2: 0.1575927734375
  self_attn.k_proj: 1.185546875
  self_attn.out_proj: 0.1884765625
  self_attn.q_proj: 1.2021484375
  self_attn.v_proj: 0.67236328125
- fc1: .inf
  fc2: 0.1693115234375
  self_attn.k_proj: 1.1416015625
  self_attn.out_proj: 0.158935546875
  self_attn.q_proj: 1.1748046875
  self_attn.v_proj: 0.68798828125
- fc1: .inf
  fc2: 0.1820068359375
  self_attn.k_proj: 1.3740234375
  self_attn.out_proj: 0.1966552734375
  self_attn.q_proj: 1.328125
  self_attn.v_proj: 0.78125
- fc1: .inf
  fc2: 0.208740234375
  self_attn.k_proj: 1.3330078125
  self_attn.out_proj: 0.169921875
  self_attn.q_proj: 1.3017578125
  self_attn.v_proj: 0.7568359375
- fc1: .inf
  fc2: 0.22021484375
  self_attn.k_proj: 1.4599609375
  self_attn.out_proj: 0.17626953125
  self_attn.q_proj: 1.41796875
  self_attn.v_proj: 0.83056640625
- fc1: .inf
  fc2: 0.2406005859375
  self_attn.k_proj: 1.3935546875
  self_attn.out_proj: 0.1300048828125
  self_attn.q_proj: 1.2529296875
  self_attn.v_proj: 0.78515625
- fc1: .inf
  fc2: 0.276123046875
  self_attn.k_proj: 1.5283203125
  self_attn.out_proj: 0.217529296875
  self_attn.q_proj: 1.419921875
  self_attn.v_proj: 0.92138671875
- fc1: .inf
  fc2: 0.302490234375
  self_attn.k_proj: 1.736328125
  self_attn.out_proj: 0.290771484375
  self_attn.q_proj: 1.720703125
  self_attn.v_proj: 1.1025390625
- fc1: .inf
  fc2: 0.350830078125
  self_attn.k_proj: 1.7001953125
  self_attn.out_proj: 0.30078125
  self_attn.q_proj: 1.716796875
  self_attn.v_proj: 1.1396484375
- fc1: .inf
  fc2: 0.384765625
  self_attn.k_proj: 1.78125
  self_attn.out_proj: 0.34814453125
  self_attn.q_proj: 1.849609375
  self_attn.v_proj: 1.2080078125
